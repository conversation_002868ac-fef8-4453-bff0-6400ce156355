
<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
        <marker id="arrowhead-normal" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
        </marker>
        <marker id="arrowhead-true" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
        </marker>
        <marker id="arrowhead-false" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#ea580c" />
        </marker>
        <marker id="arrowhead-process" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#2563eb" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Flujo asíncrono visual</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
        <g class="connection">
            <path d="M 160 160 L 160 200" stroke="#6b7280" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-normal)"/>
            
        </g>
        <g class="connection">
            <path d="M 160 260 L 360 200" stroke="#6b7280" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-normal)"/>
            
        </g>
        <g class="connection">
            <path d="M 360 260 L 260 350" stroke="#10b981" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-true)"/>
            <text x="310" y="300" text-anchor="middle" 
                           class="small-text" fill="#10b981">Resolved</text>
        </g>
        <g class="connection">
            <path d="M 360 260 L 460 350" stroke="#ea580c" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-false)"/>
            <text x="410" y="300" text-anchor="middle" 
                           class="small-text" fill="#ea580c">Rejected</text>
        </g>
        <g class="connection">
            <path d="M 260 410 L 360 450" stroke="#6b7280" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-normal)"/>
            
        </g>
        <g class="connection">
            <path d="M 460 410 L 360 450" stroke="#6b7280" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-normal)"/>
            
        </g>
        <g class="connection">
            <path d="M 360 510 L 360 550" stroke="#6b7280" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-normal)"/>
            
        </g>
        <g class="flow-node" data-id="start">
            <ellipse cx="160" cy="130" rx="60" ry="30" 
                         fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="160" y="130" 
                            text-anchor="middle" class="small-text" fill="#10b981">Inicio</text>

        </g>
        <g class="flow-node" data-id="fetch">
            <rect x="100" y="200" width="120" height="60" rx="8" 
                         fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="160" y="230" 
                            text-anchor="middle" class="small-text" fill="#2563eb">fetch(url)</text>

        </g>
        <g class="flow-node" data-id="pending">
            <polygon points="360,200 420,230 360,260 300,230" 
                         fill="#0ea5e9" fill-opacity="0.1" stroke="#0ea5e9" stroke-width="2"/>
            <text x="360" y="230" 
                            text-anchor="middle" class="small-text" fill="#0ea5e9">Promise
Pending</text>

        </g>
        <g class="flow-node" data-id="success">
            <rect x="200" y="350" width="120" height="60" rx="8" 
                         fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="260" y="380" 
                            text-anchor="middle" class="small-text" fill="#2563eb">then()</text>

        </g>
        <g class="flow-node" data-id="error">
            <rect x="400" y="350" width="120" height="60" rx="8" 
                         fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="460" y="380" 
                            text-anchor="middle" class="small-text" fill="#2563eb">catch()</text>

        </g>
        <g class="flow-node" data-id="finally">
            <rect x="300" y="450" width="120" height="60" rx="8" 
                         fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="360" y="480" 
                            text-anchor="middle" class="small-text" fill="#2563eb">finally()</text>

        </g>
        <g class="flow-node" data-id="end">
            <ellipse cx="360" cy="580" rx="60" ry="30" 
                         fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="360" y="580" 
                            text-anchor="middle" class="small-text" fill="#ea580c">Fin</text>

        </g>
        <g class="code-group">
            <rect x="500" y="150" width="300" height="200" class="code-box" rx="4" />
            <text x="515" y="175" class="code">async function fetchData() {</text>
<text x="515" y="191" class="code">  try {</text>
<text x="515" y="207" class="code">    const response = await fetch(url);</text>
<text x="515" y="223" class="code">    const data = await response.json();</text>
<text x="515" y="239" class="code">    return data;</text>
<text x="515" y="255" class="code">  } catch (error) {</text>
<text x="515" y="271" class="code">    console.error(error);</text>
<text x="515" y="287" class="code">  } finally {</text>
<text x="515" y="303" class="code">    console.log(&quot;Cleanup&quot;);</text>
<text x="515" y="319" class="code">  }</text>
<text x="515" y="335" class="code">}</text>

        </g>
    </g>
</svg>