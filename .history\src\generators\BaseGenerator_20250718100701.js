class BaseGenerator {
    constructor() {
        this.topics = {};
        this.examples = {};
        this.colors = {
            primary: '#2563eb',      // Azul para conceptos
            secondary: '#16a34a',    // Verde para ejemplos
            warning: '#ea580c',      // Naranja para advertencias
            background: '#f8fafc',   // Fondo claro
            text: '#1e293b',         // Texto principal
            code: '#374151',         // Código
            border: '#e2e8f0'        // Bordes
        };
    }

    /**
     * Genera el SVG base con estructura común
     * @param {string} title - Título del diagrama
     * @param {object} options - Opciones de configuración
     * @returns {string} - SVG base
     */
    generateBaseSVG(title, options = {}) {
        const { width = 800, height = 600 } = options;
        
        return `
<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: ${this.colors.text}; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: ${this.colors.text}; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: ${this.colors.text}; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: ${this.colors.code}; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: ${this.colors.text}; }
            .concept-box { fill: ${this.colors.primary}; fill-opacity: 0.1; stroke: ${this.colors.primary}; stroke-width: 2; }
            .example-box { fill: ${this.colors.secondary}; fill-opacity: 0.1; stroke: ${this.colors.secondary}; stroke-width: 2; }
            .warning-box { fill: ${this.colors.warning}; fill-opacity: 0.1; stroke: ${this.colors.warning}; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: ${this.colors.border}; stroke-width: 1; }
            .arrow { stroke: ${this.colors.text}; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="${this.colors.text}" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="${this.colors.background}" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">${title}</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        <!-- El contenido específico se añadirá aquí -->
    </g>
</svg>`;
    }

    /**
     * Crea una caja conceptual con texto que se ajusta automáticamente
     * @param {number} x - Posición X
     * @param {number} y - Posición Y
     * @param {number} width - Ancho
     * @param {number} height - Alto
     * @param {string} title - Título de la caja
     * @param {string} content - Contenido de la caja
     * @param {string} type - Tipo de caja (concept, example, warning)
     * @returns {string} - SVG de la caja
     */
    createBox(x, y, width, height, title, content, type = 'concept') {
        const boxClass = `${type}-box`;
        const titleY = y + 25;
        const contentStartY = y + 50;
        const padding = 15;
        const maxWidth = width - (padding * 2);

        // Dividir el contenido en líneas que caben en el ancho disponible
        const wrappedContent = this.wrapText(content, maxWidth, 14);

        let contentElements = '';
        wrappedContent.forEach((line, index) => {
            const lineY = contentStartY + (index * 18);
            contentElements += `<text x="${x + padding}" y="${lineY}" class="text">${this.escapeXML(line)}</text>\n`;
        });

        return `
        <g class="box-group">
            <rect x="${x}" y="${y}" width="${width}" height="${height}" class="${boxClass}" rx="8" />
            <text x="${x + padding}" y="${titleY}" class="subtitle">${this.escapeXML(title)}</text>
            ${contentElements}
        </g>`;
    }

    /**
     * Crea una caja de código
     * @param {number} x - Posición X
     * @param {number} y - Posición Y
     * @param {number} width - Ancho
     * @param {number} height - Alto
     * @param {string} code - Código a mostrar
     * @returns {string} - SVG de la caja de código
     */
    createCodeBox(x, y, width, height, code) {
        const lines = code.split('\n');
        let codeElements = '';
        
        lines.forEach((line, index) => {
            const lineY = y + 25 + (index * 18);
            codeElements += `<text x="${x + 15}" y="${lineY}" class="code">${this.escapeXML(line)}</text>\n`;
        });

        return `
        <g class="code-group">
            <rect x="${x}" y="${y}" width="${width}" height="${height}" class="code-box" rx="4" />
            ${codeElements}
        </g>`;
    }

    /**
     * Crea una flecha
     * @param {number} x1 - Posición X inicial
     * @param {number} y1 - Posición Y inicial
     * @param {number} x2 - Posición X final
     * @param {number} y2 - Posición Y final
     * @param {string} label - Etiqueta opcional
     * @returns {string} - SVG de la flecha
     */
    createArrow(x1, y1, x2, y2, label = '') {
        let arrow = `<line x1="${x1}" y1="${y1}" x2="${x2}" y2="${y2}" class="arrow" />`;
        
        if (label) {
            const midX = (x1 + x2) / 2;
            const midY = (y1 + y2) / 2 - 10;
            arrow += `<text x="${midX}" y="${midY}" text-anchor="middle" class="small-text">${label}</text>`;
        }
        
        return arrow;
    }

    /**
     * Escapa caracteres XML especiales
     * @param {string} text - Texto a escapar
     * @returns {string} - Texto escapado
     */
    escapeXML(text) {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }

    /**
     * Verifica si un tema está soportado
     * @param {string} topic - Tema a verificar
     * @returns {boolean} - True si está soportado
     */
    hasTopicSupport(topic) {
        return Object.keys(this.topics).includes(topic.toLowerCase());
    }

    /**
     * Obtiene los temas disponibles
     * @returns {array} - Lista de temas
     */
    getAvailableTopics() {
        return Object.keys(this.topics);
    }

    /**
     * Método abstracto para generar diagramas
     * @param {string} topic - Tema a generar
     * @param {object} options - Opciones
     * @returns {string} - SVG generado
     */
    generate(topic, options = {}) {
        throw new Error('El método generate debe ser implementado por las clases hijas');
    }

    /**
     * Obtiene un ejemplo para un tema
     * @param {string} topic - Tema
     * @returns {object} - Ejemplo
     */
    getExample(topic) {
        const normalizedTopic = topic.toLowerCase();
        if (!this.examples[normalizedTopic]) {
            throw new Error(`No hay ejemplo disponible para el tema: ${topic}`);
        }
        return this.examples[normalizedTopic];
    }
}

module.exports = BaseGenerator;
