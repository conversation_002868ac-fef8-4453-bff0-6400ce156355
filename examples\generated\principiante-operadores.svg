
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Operadores básicos</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
        <!-- Definición -->
        
        <g class="box-group">
            <rect x="50" y="80" width="700" height="80" class="concept-box" rx="8" />
            <text x="65" y="105" class="subtitle">Definición</text>
            <text x="65" y="130" class="text">Los operadores permiten realizar operaciones en variables y valores. JavaScript incluye operadores aritméticos, de asignación, de comparación y lógicos.</text>
        </g>
        
        <!-- Operadores aritméticos -->
        
        <g class="box-group">
            <rect x="50" y="180" width="320" height="150" class="concept-box" rx="8" />
            <text x="65" y="205" class="subtitle">Operadores aritméticos</text>
            <text x="65" y="230" class="text">+  Suma
-  Resta
*  Multiplicación
/  División
%  Módulo (resto)
** Exponenciación</text>
        </g>
        
        <!-- Operadores de comparación -->
        
        <g class="box-group">
            <rect x="430" y="180" width="320" height="150" class="concept-box" rx="8" />
            <text x="445" y="205" class="subtitle">Operadores de comparación</text>
            <text x="445" y="230" class="text">==  Igual (valor)
=== Igual (valor y tipo)
!=  Diferente (valor)
!== Diferente (valor y tipo)
>   Mayor que
<   Menor que</text>
        </g>
        
        <!-- Operadores lógicos -->
        
        <g class="box-group">
            <rect x="50" y="350" width="320" height="100" class="concept-box" rx="8" />
            <text x="65" y="375" class="subtitle">Operadores lógicos</text>
            <text x="65" y="400" class="text">&&  AND lógico
||  OR lógico
!   NOT lógico</text>
        </g>
        
        <!-- Operadores de asignación -->
        
        <g class="box-group">
            <rect x="430" y="350" width="320" height="100" class="concept-box" rx="8" />
            <text x="445" y="375" class="subtitle">Operadores de asignación</text>
            <text x="445" y="400" class="text">=   Asignación
+=  Suma y asignación
-=  Resta y asignación</text>
        </g>
        
        <!-- Ejemplo práctico -->
        
        <g class="code-group">
            <rect x="50" y="470" width="700" height="120" class="code-box" rx="4" />
            <text x="65" y="495" class="code">let a = 5;</text>
<text x="65" y="513" class="code">let b = 2;</text>
<text x="65" y="531" class="code"></text>
<text x="65" y="549" class="code">let suma = a + b;     // 7</text>
<text x="65" y="567" class="code">let comparacion = a &gt; b;  // true</text>
<text x="65" y="585" class="code">let logico = (a &gt; 0) &amp;&amp; (b &lt; 5);  // true</text>

        </g>
        
    </g>
</svg>