
<svg width="1000" height="700" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Scope y hoisting</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
        <!-- Prerequisitos -->
        
        <g class="box-group">
            <rect x="50" y="80" width="900" height="60" class="warning-box" rx="8" />
            <text x="65" y="105" class="subtitle">Prerequisitos</text>
            <text x="65" y="130" class="text">Variables (var, let, const), Funciones básicas</text>

        </g>
        
        <!-- Definición de Scope -->
        
        <g class="box-group">
            <rect x="50" y="160" width="430" height="120" class="concept-box" rx="8" />
            <text x="65" y="185" class="subtitle">Scope (Ámbito)</text>
            <text x="65" y="210" class="text">El scope determina dónde las variables son</text>
<text x="65" y="228" class="text">accesibles en el código.

Tipos:
• Global</text>
<text x="65" y="246" class="text">scope: Accesible en todo el programa
• Function</text>
<text x="65" y="264" class="text">scope: Solo dentro de la función
• Block scope:</text>
<text x="65" y="282" class="text">Solo dentro del bloque {}</text>

        </g>
        
        <!-- Hoisting -->
        
        <g class="box-group">
            <rect x="520" y="160" width="430" height="120" class="concept-box" rx="8" />
            <text x="535" y="185" class="subtitle">Hoisting</text>
            <text x="535" y="210" class="text">JavaScript &quot;eleva&quot; las declaraciones al inicio</text>
<text x="535" y="228" class="text">de su scope.

• var: Se eleva, se inicializa</text>
<text x="535" y="246" class="text">con undefined
• let/const: Se elevan, pero no</text>
<text x="535" y="264" class="text">se inicializan
• function: Se eleva</text>
<text x="535" y="282" class="text">completamente</text>

        </g>
        
        <!-- Ejemplo de Scope -->
        
        <g class="code-group">
            <rect x="50" y="300" width="430" height="150" class="code-box" rx="4" />
            <text x="65" y="325" class="code">var globalVar = &quot;Global&quot;;</text>
<text x="65" y="341" class="code"></text>
<text x="65" y="357" class="code">function ejemplo() {</text>
<text x="65" y="373" class="code">  var funcionVar = &quot;Función&quot;;</text>
<text x="65" y="389" class="code">  </text>
<text x="65" y="405" class="code">  if (true) {</text>
<text x="65" y="421" class="code">    let bloqueVar = &quot;Bloque&quot;;</text>
<text x="65" y="437" class="code">    const bloqueConst = &quot;Constante&quot;;</text>
<text x="65" y="453" class="code">  }</text>
<text x="65" y="469" class="code">  </text>
<text x="65" y="485" class="code">  // bloqueVar no es accesible aquí</text>
<text x="65" y="501" class="code">}</text>

        </g>
        
        <!-- Ejemplo de Hoisting -->
        
        <g class="code-group">
            <rect x="520" y="300" width="430" height="150" class="code-box" rx="4" />
            <text x="535" y="325" class="code">// Lo que escribes:</text>
<text x="535" y="341" class="code">console.log(x); // undefined</text>
<text x="535" y="357" class="code">var x = 5;</text>
<text x="535" y="373" class="code"></text>
<text x="535" y="389" class="code">// Lo que JavaScript interpreta:</text>
<text x="535" y="405" class="code">var x;</text>
<text x="535" y="421" class="code">console.log(x); // undefined</text>
<text x="535" y="437" class="code">x = 5;</text>
<text x="535" y="453" class="code"></text>
<text x="535" y="469" class="code">// Con let/const da error:</text>
<text x="535" y="485" class="code">console.log(y); // ReferenceError</text>
<text x="535" y="501" class="code">let y = 10;</text>

        </g>
        
        <!-- Buenas prácticas -->
        
        <g class="box-group">
            <rect x="50" y="470" width="900" height="80" class="example-box" rx="8" />
            <text x="65" y="495" class="subtitle">Buenas prácticas</text>
            <text x="65" y="520" class="text">Usar let y const en lugar de var. Declarar variables al inicio de su scope. Evitar variables globales</text>
<text x="65" y="538" class="text">innecesarias. Usar const por defecto, let cuando necesites reasignar.</text>

        </g>
        
        <!-- Flecha explicativa -->
        <line x1="280" y1="280" x2="280" y2="300" class="arrow" /><text x="280" y="280" text-anchor="middle" class="small-text">Scope</text>
        <line x1="720" y1="280" x2="720" y2="300" class="arrow" /><text x="720" y="280" text-anchor="middle" class="small-text">Hoisting</text>
        
    </g>
</svg>