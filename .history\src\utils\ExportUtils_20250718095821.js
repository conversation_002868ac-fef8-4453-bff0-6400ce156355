const fs = require('fs');
const path = require('path');

class ExportUtils {
    constructor() {
        this.supportedFormats = ['svg', 'png', 'pdf', 'html'];
    }

    /**
     * Exporta un diagrama SVG a diferentes formatos
     * @param {string} svg - Contenido SVG
     * @param {string} format - Formato de exportación
     * @param {string} filename - Nombre del archivo
     * @param {string} outputDir - Directorio de salida
     * @returns {string} - Ruta del archivo generado
     */
    async exportDiagram(svg, format, filename, outputDir = './exports') {
        if (!this.supportedFormats.includes(format)) {
            throw new Error(`Formato no soportado: ${format}. Formatos disponibles: ${this.supportedFormats.join(', ')}`);
        }

        // Crear directorio de salida si no existe
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        const outputPath = path.join(outputDir, `${filename}.${format}`);

        switch (format) {
            case 'svg':
                return this.exportSVG(svg, outputPath);
            case 'html':
                return this.exportHTML(svg, outputPath, filename);
            case 'png':
                return this.exportPNG(svg, outputPath);
            case 'pdf':
                return this.exportPDF(svg, outputPath);
            default:
                throw new Error(`Formato no implementado: ${format}`);
        }
    }

    /**
     * Exporta como archivo SVG
     * @param {string} svg - Contenido SVG
     * @param {string} outputPath - Ruta de salida
     * @returns {string} - Ruta del archivo generado
     */
    exportSVG(svg, outputPath) {
        fs.writeFileSync(outputPath, svg, 'utf8');
        return outputPath;
    }

    /**
     * Exporta como archivo HTML con el SVG embebido
     * @param {string} svg - Contenido SVG
     * @param {string} outputPath - Ruta de salida
     * @param {string} title - Título del documento
     * @returns {string} - Ruta del archivo generado
     */
    exportHTML(svg, outputPath, title) {
        const htmlContent = this.generateHTMLTemplate(svg, title);
        fs.writeFileSync(outputPath, htmlContent, 'utf8');
        return outputPath;
    }

    /**
     * Genera una plantilla HTML para el SVG
     * @param {string} svg - Contenido SVG
     * @param {string} title - Título del documento
     * @returns {string} - Contenido HTML
     */
    generateHTMLTemplate(svg, title) {
        return `<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title} - JavaScript Diagram</title>
    <style>
        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #1e293b;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #64748b;
            font-size: 16px;
        }
        
        .diagram-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            max-width: 100%;
            overflow: auto;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #64748b;
            font-size: 14px;
        }
        
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .btn {
            background: #2563eb;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }
        
        .btn:hover {
            background: #1d4ed8;
        }
        
        @media print {
            .controls, .footer { display: none; }
            body { background: white; }
            .diagram-container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>${title}</h1>
        <p>Diagrama educativo de JavaScript</p>
    </div>
    
    <div class="controls">
        <button class="btn" onclick="window.print()">🖨️ Imprimir</button>
        <button class="btn" onclick="downloadSVG()">📥 Descargar SVG</button>
        <button class="btn" onclick="copyToClipboard()">📋 Copiar</button>
    </div>
    
    <div class="diagram-container">
        ${svg}
    </div>
    
    <div class="footer">
        <p>Generado con JavaScript Diagram Generator</p>
        <p>Fecha: ${new Date().toLocaleDateString('es-ES')}</p>
    </div>

    <script>
        function downloadSVG() {
            const svgElement = document.querySelector('svg');
            const svgData = new XMLSerializer().serializeToString(svgElement);
            const blob = new Blob([svgData], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.svg';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        async function copyToClipboard() {
            const svgElement = document.querySelector('svg');
            const svgData = new XMLSerializer().serializeToString(svgElement);
            
            try {
                await navigator.clipboard.writeText(svgData);
                alert('SVG copiado al portapapeles');
            } catch (err) {
                console.error('Error copiando al portapapeles:', err);
                alert('Error copiando al portapapeles');
            }
        }
    </script>
</body>
</html>`;
    }

    /**
     * Exporta como PNG (requiere librería externa)
     * @param {string} svg - Contenido SVG
     * @param {string} outputPath - Ruta de salida
     * @returns {string} - Ruta del archivo generado
     */
    exportPNG(svg, outputPath) {
        // Nota: Para implementar PNG se necesitaría una librería como puppeteer o sharp
        // Por ahora, guardamos las instrucciones para implementar más tarde
        const instructions = `
Para exportar a PNG, instala puppeteer:
npm install puppeteer

Luego implementa la conversión SVG -> PNG usando puppeteer para renderizar el SVG en un navegador headless.
        `;
        
        fs.writeFileSync(outputPath.replace('.png', '_instructions.txt'), instructions, 'utf8');
        throw new Error('Exportación a PNG no implementada. Se requiere puppeteer. Ver archivo de instrucciones generado.');
    }

    /**
     * Exporta como PDF (requiere librería externa)
     * @param {string} svg - Contenido SVG
     * @param {string} outputPath - Ruta de salida
     * @returns {string} - Ruta del archivo generado
     */
    exportPDF(svg, outputPath) {
        // Nota: Para implementar PDF se necesitaría una librería como puppeteer o jsPDF
        const instructions = `
Para exportar a PDF, instala puppeteer:
npm install puppeteer

Luego implementa la conversión SVG -> PDF usando puppeteer para generar un PDF del SVG renderizado.
        `;
        
        fs.writeFileSync(outputPath.replace('.pdf', '_instructions.txt'), instructions, 'utf8');
        throw new Error('Exportación a PDF no implementada. Se requiere puppeteer. Ver archivo de instrucciones generado.');
    }

    /**
     * Exporta múltiples diagramas en lote
     * @param {Array} diagrams - Array de objetos {svg, filename, level, topic}
     * @param {string} format - Formato de exportación
     * @param {string} outputDir - Directorio de salida
     * @returns {Array} - Array de rutas de archivos generados
     */
    async exportBatch(diagrams, format, outputDir = './exports') {
        const results = [];
        
        for (const diagram of diagrams) {
            try {
                const filename = `${diagram.level}-${diagram.topic}`;
                const outputPath = await this.exportDiagram(diagram.svg, format, filename, outputDir);
                results.push({
                    success: true,
                    filename: diagram.filename || filename,
                    outputPath,
                    level: diagram.level,
                    topic: diagram.topic
                });
            } catch (error) {
                results.push({
                    success: false,
                    filename: diagram.filename || `${diagram.level}-${diagram.topic}`,
                    error: error.message,
                    level: diagram.level,
                    topic: diagram.topic
                });
            }
        }
        
        return results;
    }

    /**
     * Obtiene información sobre los formatos soportados
     * @returns {Object} - Información de formatos
     */
    getFormatInfo() {
        return {
            svg: {
                name: 'SVG',
                description: 'Scalable Vector Graphics - Formato vectorial nativo',
                extension: '.svg',
                implemented: true
            },
            html: {
                name: 'HTML',
                description: 'Página web con SVG embebido',
                extension: '.html',
                implemented: true
            },
            png: {
                name: 'PNG',
                description: 'Portable Network Graphics - Imagen rasterizada',
                extension: '.png',
                implemented: false,
                requirements: 'Requiere puppeteer'
            },
            pdf: {
                name: 'PDF',
                description: 'Portable Document Format',
                extension: '.pdf',
                implemented: false,
                requirements: 'Requiere puppeteer'
            }
        };
    }
}

module.exports = ExportUtils;
