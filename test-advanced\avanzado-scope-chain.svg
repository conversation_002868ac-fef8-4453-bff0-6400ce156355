
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
        <marker id="arrowhead-normal" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
        </marker>
        <marker id="arrowhead-true" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
        </marker>
        <marker id="arrowhead-false" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#ea580c" />
        </marker>
        <marker id="arrowhead-process" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#2563eb" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Cadena de scope visual</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
            <g class="scope-container">
                <rect x="50" y="100" width="250" height="120" rx="8"
                      fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
                <text x="60" y="125" class="subtitle" fill="#2563eb">
                    Global Scope
                </text>
            </g><text x="65" y="145" class="small-text" fill="#1e293b">
                            globalVar: string
                            </text><text x="65" y="165" class="small-text" fill="#1e293b">
                            outerFunction: function
                            </text>
            <g class="scope-container">
                <rect x="100" y="250" width="250" height="120" rx="8"
                      fill="#10b981" fill-opacity="0.1" stroke="#10b981" stroke-width="2"/>
                <text x="110" y="275" class="subtitle" fill="#10b981">
                    Function Scope
                </text>
            </g><text x="115" y="295" class="small-text" fill="#1e293b">
                            localVar: number
                            </text><text x="115" y="315" class="small-text" fill="#1e293b">
                            innerFunction: function
                            </text><line x1="100" y1="325" x2="300" y2="160" 
                stroke="#6b7280" stroke-width="1" stroke-dasharray="5,5" 
                marker-end="url(#arrowhead)"/>
            <g class="scope-container">
                <rect x="150" y="400" width="250" height="120" rx="8"
                      fill="#0ea5e9" fill-opacity="0.1" stroke="#0ea5e9" stroke-width="2"/>
                <text x="160" y="425" class="subtitle" fill="#0ea5e9">
                    Block Scope
                </text>
            </g><text x="165" y="445" class="small-text" fill="#1e293b">
                            blockVar: boolean
                            </text><text x="165" y="465" class="small-text" fill="#1e293b">
                            tempVar: object
                            </text><line x1="150" y1="475" x2="350" y2="310" 
                stroke="#6b7280" stroke-width="1" stroke-dasharray="5,5" 
                marker-end="url(#arrowhead)"/>
        <g class="code-group">
            <rect x="400" y="100" width="350" height="200" class="code-box" rx="4" />
            <text x="415" y="125" class="code">let globalVar = &quot;global&quot;;</text>
<text x="415" y="141" class="code"></text>
<text x="415" y="157" class="code">function outerFunction() {</text>
<text x="415" y="173" class="code">  let localVar = 42;</text>
<text x="415" y="189" class="code">  </text>
<text x="415" y="205" class="code">  function innerFunction() {</text>
<text x="415" y="221" class="code">    if (true) {</text>
<text x="415" y="237" class="code">      let blockVar = true;</text>
<text x="415" y="253" class="code">      // Acceso a todas las variables</text>
<text x="415" y="269" class="code">      console.log(globalVar, localVar, blockVar);</text>
<text x="415" y="285" class="code">    }</text>
<text x="415" y="301" class="code">  }</text>
<text x="415" y="317" class="code">}</text>

        </g>
        <g class="box-group">
            <rect x="400" y="320" width="350" height="150" class="example-box" rx="8" />
            <text x="415" y="345" class="subtitle">Lookup de Variables</text>
            <text x="415" y="370" class="text">JavaScript busca variables siguiendo</text>
<text x="415" y="388" class="text">la cadena de scope: 1) Scope actual,</text>
<text x="415" y="406" class="text">2) Scope padre, 3) Scope global. Si no</text>
<text x="415" y="424" class="text">encuentra la variable, lanza</text>
<text x="415" y="442" class="text">ReferenceError.</text>

        </g>
    </g>
</svg>