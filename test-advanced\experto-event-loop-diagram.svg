
<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
        <marker id="arrowhead-normal" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
        </marker>
        <marker id="arrowhead-true" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
        </marker>
        <marker id="arrowhead-false" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#ea580c" />
        </marker>
        <marker id="arrowhead-process" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#2563eb" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Diagrama detallado del Event Loop</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
            <rect x="100" y="100" width="150" height="200"
                  fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2" rx="8"/>
            <text x="175" y="125" text-anchor="middle"
                  class="subtitle" fill="#2563eb">Call Stack</text>
                <rect x="110" y="140" width="130" height="30"
                      fill="#2563eb" fill-opacity="0.3" rx="4"/>
                <text x="175" y="160" text-anchor="middle"
                      class="small-text">main()</text>
                <rect x="110" y="180" width="130" height="30"
                      fill="#2563eb" fill-opacity="0.3" rx="4"/>
                <text x="175" y="200" text-anchor="middle"
                      class="small-text">setTimeout()</text>
            <rect x="300" y="100" width="150" height="100"
                  fill="#16a34a" fill-opacity="0.1" stroke="#16a34a" stroke-width="2" rx="8"/>
            <text x="375" y="125" text-anchor="middle"
                  class="subtitle" fill="#16a34a">Heap</text>
            <rect x="500" y="100" width="150" height="150"
                  fill="undefined" fill-opacity="0.1" stroke="undefined" stroke-width="2" rx="8"/>
            <text x="575" y="125" text-anchor="middle"
                  class="subtitle" fill="undefined">Web APIs</text>
                <text x="510" y="150" class="small-text">• setTimeout</text>
                <text x="510" y="170" class="small-text">• fetch</text>
                <text x="510" y="190" class="small-text">• DOM events</text>
            <rect x="300" y="300" width="200" height="80"
                  fill="#ea580c" fill-opacity="0.1" stroke="#ea580c" stroke-width="2" rx="8"/>
            <text x="400" y="325" text-anchor="middle"
                  class="subtitle" fill="#ea580c">Callback Queue</text>
                <rect x="310" y="330" width="60" height="30"
                      fill="#ea580c" fill-opacity="0.3" rx="4"/>
                <text x="340" y="350" text-anchor="middle"
                      class="small-text">callback</text>
            <rect x="300" y="400" width="200" height="80"
                  fill="undefined" fill-opacity="0.1" stroke="undefined" stroke-width="2" rx="8"/>
            <text x="400" y="425" text-anchor="middle"
                  class="subtitle" fill="undefined">Microtask Queue</text>
                <rect x="310" y="430" width="60" height="30"
                      fill="undefined" fill-opacity="0.3" rx="4"/>
                <text x="340" y="450" text-anchor="middle"
                      class="small-text">promise</text>
            <rect x="550" y="350" width="100" height="60"
                  fill="undefined" fill-opacity="0.1" stroke="undefined" stroke-width="2" rx="8"/>
            <text x="600" y="375" text-anchor="middle"
                  class="subtitle" fill="undefined">Event Loop</text>
                <line x1="250" y1="150" x2="300" y2="150"
                      stroke="#2563eb" stroke-width="2" marker-end="url(#arrowhead)"/><text x="275" y="140" text-anchor="middle" class="small-text"
                   fill="#2563eb">allocate</text>
                <line x1="250" y1="200" x2="500" y2="150"
                      stroke="#2563eb" stroke-width="2" marker-end="url(#arrowhead)"/><text x="375" y="165" text-anchor="middle" class="small-text"
                   fill="#2563eb">async call</text>
                <line x1="575" y1="250" x2="400" y2="300"
                      stroke="#2563eb" stroke-width="2" marker-end="url(#arrowhead)"/><text x="487.5" y="265" text-anchor="middle" class="small-text"
                   fill="#2563eb">callback</text>
                <line x1="500" y1="340" x2="550" y2="360"
                      stroke="#2563eb" stroke-width="2" marker-end="url(#arrowhead)"/><text x="525" y="340" text-anchor="middle" class="small-text"
                   fill="#2563eb">check</text>
                <path d="M 550 380 Q 450 430 200 300"
                      stroke="#2563eb" stroke-width="2" fill="none"
                      marker-end="url(#arrowhead)" stroke-dasharray="5,5"/><text x="375" y="330" text-anchor="middle" class="small-text"
                   fill="#2563eb">push to stack</text>
        <g class="code-group">
            <rect x="50" y="520" width="600" height="120" class="code-box" rx="4" />
            <text x="65" y="545" class="code">console.log(&quot;1&quot;);</text>
<text x="65" y="561" class="code">setTimeout(() =&gt; console.log(&quot;2&quot;), 0);</text>
<text x="65" y="577" class="code">Promise.resolve().then(() =&gt; console.log(&quot;3&quot;));</text>
<text x="65" y="593" class="code">console.log(&quot;4&quot;);</text>
<text x="65" y="609" class="code"></text>
<text x="65" y="625" class="code">// Output: 1, 4, 3, 2 (microtasks tienen prioridad)</text>

        </g>
    </g>
</svg>