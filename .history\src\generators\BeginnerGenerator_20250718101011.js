const BaseGenerator = require('./BaseGenerator');

class BeginnerGenerator extends BaseGenerator {
    constructor() {
        super();
        
        // Definir temas disponibles para nivel principiante
        this.topics = {
            'variables': 'Variables y tipos de datos',
            'operadores': 'Operadores básicos',
            'condicionales': 'Condicionales (if/else)',
            'bucles': 'Bucles (for, while)',
            'funciones': 'Funciones básicas',
            'arrays': 'Arrays y objetos simples',
            'strings': 'Métodos de string básicos',
            'objetos': 'Objetos y propiedades',
            'eventos': 'Eventos básicos',
            'dom-basico': 'DOM básico',
            'errores': 'Manejo de errores básico',
            'json': 'JSON y datos',
            'fechas': 'Fechas y tiempo',
            'math': 'Matemáticas en JavaScript',
            'regexp': 'Expresiones regulares básicas'
        };
        
        // Ejemplos predefinidos
        this.examples = {
            'variables': this.generateVariablesExample(),
            'operadores': this.generateOperatorsExample(),
            'condicionales': this.generateConditionalsExample(),
            'bucles': this.generateLoopsExample(),
            'funciones': this.generateFunctionsExample(),
            'arrays': this.generateArraysExample(),
            'strings': this.generateStringsExample()
        };
    }

    /**
     * Genera un diagrama SVG para un tema de nivel principiante
     * @param {string} topic - Tema a generar
     * @param {object} options - Opciones adicionales
     * @returns {string} - SVG generado
     */
    generate(topic, options = {}) {
        const normalizedTopic = topic.toLowerCase();
        
        if (!this.hasTopicSupport(normalizedTopic)) {
            throw new Error(`El tema "${topic}" no está soportado para nivel principiante`);
        }
        
        // Obtener el título completo del tema
        const title = this.topics[normalizedTopic];
        
        // Generar el SVG base
        let svg = this.generateBaseSVG(title, options);
        
        // Generar el contenido específico según el tema
        let content = '';
        switch (normalizedTopic) {
            case 'variables':
                content = this.generateVariablesContent();
                break;
            case 'operadores':
                content = this.generateOperatorsContent();
                break;
            case 'condicionales':
                content = this.generateConditionalsContent();
                break;
            case 'bucles':
                content = this.generateLoopsContent();
                break;
            case 'funciones':
                content = this.generateFunctionsContent();
                break;
            case 'arrays':
                content = this.generateArraysContent();
                break;
            case 'strings':
                content = this.generateStringsContent();
                break;
            default:
                content = '<text x="400" y="300" text-anchor="middle" class="text">Contenido no disponible</text>';
        }
        
        // Insertar el contenido en el SVG
        svg = svg.replace('<!-- El contenido específico se añadirá aquí -->', content);
        
        return svg;
    }

    /**
     * Genera el contenido para el tema de variables usando layout automático
     * @returns {string} - SVG del contenido
     */
    generateVariablesContent() {
        const elements = [
            {
                type: 'concept',
                title: 'Definición',
                content: 'Las variables son contenedores para almacenar datos. En JavaScript existen tres formas de declarar variables: var, let y const.'
            },
            {
                type: 'concept',
                title: 'Declaración con var',
                content: 'var nombre = valor; Tiene ámbito de función, puede redeclararse y reasignarse. Se eleva (hoisting) al inicio de la función.'
            },
            {
                type: 'concept',
                title: 'Declaración con let',
                content: 'let nombre = valor; Tiene ámbito de bloque, no puede redeclararse pero sí reasignarse. Más seguro que var.'
            },
            {
                type: 'concept',
                title: 'Declaración con const',
                content: 'const nombre = valor; Tiene ámbito de bloque, no puede redeclararse ni reasignarse. Debe inicializarse al declararla.'
            },
            {
                type: 'concept',
                title: 'Tipos de datos primitivos',
                content: 'String: "Hola mundo" - Number: 42, 3.14 - Boolean: true, false - Undefined: undefined - Null: null - Symbol: Symbol("id")'
            },
            {
                type: 'concept',
                title: 'Tipos de datos complejos',
                content: 'Object: {nombre: "Ana", edad: 25} - Array: [1, 2, 3, "texto"] - Function: function() {} - Date: new Date()'
            },
            {
                type: 'code',
                title: 'Ejemplo práctico',
                content: 'let nombre = "Ana";\nconst edad = 25;\nlet esEstudiante = true;\nlet hobbies = ["leer", "programar"];\n\nconsole.log(nombre); // "Ana"\nconsole.log(typeof edad); // "number"\nconsole.log(esEstudiante); // true'
            },
            {
                type: 'warning',
                title: 'Errores comunes',
                content: 'Usar const para valores que necesitan cambiar. Usar var en lugar de let. No inicializar variables. Confundir null con undefined.'
            },
            {
                type: 'example',
                title: 'Buenas prácticas',
                content: 'Usar const por defecto. Usar let cuando necesites reasignar. Evitar var. Usar nombres descriptivos. Inicializar variables al declararlas.'
            }
        ];

        const layout = this.createAutoLayout(elements, {
            startX: 50,
            startY: 80,
            maxWidth: 1100,
            columnsPreferred: 2
        });

        let content = '';
        layout.elements.forEach(element => {
            if (element.type === 'code') {
                content += this.createCodeBox(element.x, element.y, element.width, element.height, element.content);
            } else {
                content += this.createBox(element.x, element.y, element.width, element.height, element.title, element.content, element.type);
            }
        });

        return content;
    }

    /**
     * Genera el contenido para el tema de operadores
     * @returns {string} - SVG del contenido
     */
    generateOperatorsContent() {
        return `
        <!-- Definición -->
        ${this.createBox(50, 80, 700, 80, 'Definición', 'Los operadores permiten realizar operaciones en variables y valores. JavaScript incluye operadores aritméticos, de asignación, de comparación y lógicos.', 'concept')}
        
        <!-- Operadores aritméticos -->
        ${this.createBox(50, 180, 320, 150, 'Operadores aritméticos', '+  Suma\n-  Resta\n*  Multiplicación\n/  División\n%  Módulo (resto)\n** Exponenciación', 'concept')}
        
        <!-- Operadores de comparación -->
        ${this.createBox(430, 180, 320, 150, 'Operadores de comparación', '==  Igual (valor)\n=== Igual (valor y tipo)\n!=  Diferente (valor)\n!== Diferente (valor y tipo)\n>   Mayor que\n<   Menor que', 'concept')}
        
        <!-- Operadores lógicos -->
        ${this.createBox(50, 350, 320, 100, 'Operadores lógicos', '&&  AND lógico\n||  OR lógico\n!   NOT lógico', 'concept')}
        
        <!-- Operadores de asignación -->
        ${this.createBox(430, 350, 320, 100, 'Operadores de asignación', '=   Asignación\n+=  Suma y asignación\n-=  Resta y asignación', 'concept')}
        
        <!-- Ejemplo práctico -->
        ${this.createCodeBox(50, 470, 700, 120, 'let a = 5;\nlet b = 2;\n\nlet suma = a + b;     // 7\nlet comparacion = a > b;  // true\nlet logico = (a > 0) && (b < 5);  // true')}
        `;
    }

    /**
     * Genera el contenido para el tema de condicionales
     * @returns {string} - SVG del contenido
     */
    generateConditionalsContent() {
        return `
        <!-- Definición -->
        ${this.createBox(50, 80, 700, 80, 'Definición', 'Las estructuras condicionales permiten ejecutar diferentes bloques de código dependiendo de si una condición es verdadera o falsa.', 'concept')}
        
        <!-- if/else -->
        ${this.createBox(50, 180, 320, 150, 'if/else', 'if (condición) {\n  // código si es verdadero\n} else {\n  // código si es falso\n}', 'concept')}
        
        <!-- if/else if/else -->
        ${this.createBox(430, 180, 320, 150, 'if/else if/else', 'if (condición1) {\n  // código si condición1 es verdadera\n} else if (condición2) {\n  // código si condición2 es verdadera\n} else {\n  // código si ninguna es verdadera\n}', 'concept')}
        
        <!-- Operador ternario -->
        ${this.createBox(50, 350, 320, 100, 'Operador ternario', 'condición ? valorSiVerdadero : valorSiFalso', 'concept')}
        
        <!-- Switch -->
        ${this.createBox(430, 350, 320, 150, 'Switch', 'switch(expresión) {\n  case valor1:\n    // código\n    break;\n  case valor2:\n    // código\n    break;\n  default:\n    // código por defecto\n}', 'concept')}
        
        <!-- Ejemplo práctico -->
        ${this.createCodeBox(50, 520, 700, 120, 'let edad = 18;\n\nif (edad >= 18) {\n  console.log("Eres mayor de edad");\n} else {\n  console.log("Eres menor de edad");\n}\n\n// Usando operador ternario\nlet mensaje = edad >= 18 ? "Mayor de edad" : "Menor de edad";')}
        `;
    }

    /**
     * Genera el contenido para el tema de bucles
     * @returns {string} - SVG del contenido
     */
    generateLoopsContent() {
        return `
        <!-- Definición -->
        ${this.createBox(50, 80, 700, 80, 'Definición', 'Los bucles permiten ejecutar un bloque de código repetidamente mientras se cumpla una condición específica.', 'concept')}

        <!-- For loop -->
        ${this.createBox(50, 180, 320, 150, 'Bucle for', 'for (inicialización; condición; incremento) {\n  // código a repetir\n}\n\nEjemplo:\nfor (let i = 0; i < 5; i++) {\n  console.log(i);\n}', 'concept')}

        <!-- While loop -->
        ${this.createBox(430, 180, 320, 150, 'Bucle while', 'while (condición) {\n  // código a repetir\n}\n\nEjemplo:\nlet i = 0;\nwhile (i < 5) {\n  console.log(i);\n  i++;\n}', 'concept')}

        <!-- Do-while loop -->
        ${this.createBox(50, 350, 320, 120, 'Bucle do-while', 'do {\n  // código a repetir\n} while (condición);\n\nSe ejecuta al menos una vez', 'concept')}

        <!-- Break y Continue -->
        ${this.createBox(430, 350, 320, 120, 'Break y Continue', 'break: Sale del bucle\ncontinue: Salta a la siguiente iteración', 'concept')}

        <!-- Ejemplo práctico -->
        ${this.createCodeBox(50, 490, 700, 100, 'for (let i = 1; i <= 10; i++) {\n  if (i % 2 === 0) {\n    console.log(i + " es par");\n  }\n}')}
        `;
    }

    /**
     * Genera el contenido para el tema de funciones
     * @returns {string} - SVG del contenido
     */
    generateFunctionsContent() {
        return `
        <!-- Definición -->
        ${this.createBox(50, 80, 700, 80, 'Definición', 'Las funciones son bloques de código reutilizables que realizan una tarea específica. Pueden recibir parámetros y devolver valores.', 'concept')}

        <!-- Declaración de función -->
        ${this.createBox(50, 180, 320, 150, 'Declaración de función', 'function nombreFuncion(parametros) {\n  // código de la función\n  return valor; // opcional\n}\n\nEjemplo:\nfunction saludar(nombre) {\n  return "Hola " + nombre;\n}', 'concept')}

        <!-- Expresión de función -->
        ${this.createBox(430, 180, 320, 150, 'Expresión de función', 'const nombreFuncion = function(parametros) {\n  // código de la función\n};\n\nEjemplo:\nconst sumar = function(a, b) {\n  return a + b;\n};', 'concept')}

        <!-- Llamada a función -->
        ${this.createBox(50, 350, 320, 100, 'Llamada a función', 'nombreFuncion(argumentos);\n\nEjemplo:\nlet resultado = sumar(5, 3);', 'concept')}

        <!-- Ámbito de variables -->
        ${this.createBox(430, 350, 320, 100, 'Ámbito (Scope)', 'Variables locales: Solo dentro de la función\nVariables globales: Accesibles desde cualquier lugar', 'concept')}

        <!-- Ejemplo práctico -->
        ${this.createCodeBox(50, 470, 700, 120, 'function calcularArea(radio) {\n  const pi = 3.14159;\n  return pi * radio * radio;\n}\n\nlet area = calcularArea(5);\nconsole.log("El área es: " + area);')}
        `;
    }

    /**
     * Genera el contenido para el tema de arrays
     * @returns {string} - SVG del contenido
     */
    generateArraysContent() {
        return `
        <!-- Definición -->
        ${this.createBox(50, 80, 700, 80, 'Definición', 'Los arrays son estructuras de datos que almacenan múltiples valores en una sola variable. Los objetos almacenan pares clave-valor.', 'concept')}

        <!-- Arrays -->
        ${this.createBox(50, 180, 320, 150, 'Arrays', 'let array = [elemento1, elemento2, ...];\n\nAcceso: array[índice]\nLongitud: array.length\n\nEjemplo:\nlet frutas = ["manzana", "banana"];\nconsole.log(frutas[0]); // "manzana"', 'concept')}

        <!-- Objetos -->
        ${this.createBox(430, 180, 320, 150, 'Objetos', 'let objeto = {\n  clave1: valor1,\n  clave2: valor2\n};\n\nAcceso: objeto.clave o objeto["clave"]\n\nEjemplo:\nlet persona = {nombre: "Ana", edad: 25};', 'concept')}

        <!-- Métodos de array -->
        ${this.createBox(50, 350, 320, 120, 'Métodos básicos de array', 'push(): Añade al final\npop(): Elimina del final\nshift(): Elimina del inicio\nunshift(): Añade al inicio\nlength: Longitud del array', 'concept')}

        <!-- Propiedades de objeto -->
        ${this.createBox(430, 350, 320, 120, 'Trabajar con objetos', 'Añadir: objeto.nuevaClave = valor\nEliminar: delete objeto.clave\nVerificar: "clave" in objeto', 'concept')}

        <!-- Ejemplo práctico -->
        ${this.createCodeBox(50, 490, 700, 100, 'let estudiantes = ["Ana", "Luis", "María"];\nestudiantes.push("Carlos");\n\nlet estudiante = {nombre: "Ana", edad: 20, curso: "JavaScript"};\nconsole.log(estudiante.nombre + " tiene " + estudiante.edad + " años");')}
        `;
    }

    /**
     * Genera el contenido para el tema de strings
     * @returns {string} - SVG del contenido
     */
    generateStringsContent() {
        return `
        <!-- Definición -->
        ${this.createBox(50, 80, 700, 80, 'Definición', 'Los strings (cadenas de texto) son secuencias de caracteres. JavaScript proporciona muchos métodos para manipular strings.', 'concept')}

        <!-- Creación de strings -->
        ${this.createBox(50, 180, 320, 120, 'Creación de strings', 'let str1 = "Comillas dobles";\nlet str2 = \'Comillas simples\';\nlet str3 = \`Template literal\`;\n\nConcatenación: str1 + str2', 'concept')}

        <!-- Métodos básicos -->
        ${this.createBox(430, 180, 320, 150, 'Métodos básicos', 'length: Longitud del string\ntoUpperCase(): A mayúsculas\ntoLowerCase(): A minúsculas\nindexOf(): Buscar posición\nslice(): Extraer parte\nreplace(): Reemplazar texto', 'concept')}

        <!-- Template literals -->
        ${this.createBox(50, 320, 320, 100, 'Template literals', 'let nombre = "Ana";\nlet edad = 25;\nlet mensaje = \`Hola \${nombre}, tienes \${edad} años\`;', 'concept')}

        <!-- Escape de caracteres -->
        ${this.createBox(430, 320, 320, 100, 'Caracteres especiales', '\\n: Nueva línea\n\\t: Tabulación\n\\": Comilla doble\n\\\': Comilla simple\n\\\\: Barra invertida', 'concept')}

        <!-- Ejemplo práctico -->
        ${this.createCodeBox(50, 440, 700, 120, 'let texto = "JavaScript es genial";\nconsole.log(texto.length);        // 20\nconsole.log(texto.toUpperCase()); // "JAVASCRIPT ES GENIAL"\nconsole.log(texto.indexOf("Script")); // 4\nconsole.log(texto.slice(0, 10));  // "JavaScript"')}
        `;
    }

    // Métodos para generar ejemplos
    generateVariablesExample() {
        return {
            svg: this.generate('variables'),
            description: 'Diagrama que muestra los conceptos básicos de variables en JavaScript, incluyendo declaración, tipos de datos y ejemplos de uso.'
        };
    }

    generateOperatorsExample() {
        return {
            svg: this.generate('operadores'),
            description: 'Diagrama que muestra los diferentes tipos de operadores en JavaScript: aritméticos, de comparación, lógicos y de asignación.'
        };
    }

    generateConditionalsExample() {
        return {
            svg: this.generate('condicionales'),
            description: 'Diagrama que explica las estructuras condicionales en JavaScript, incluyendo if/else, operador ternario y switch.'
        };
    }

    generateLoopsExample() {
        return {
            svg: this.generate('bucles'),
            description: 'Diagrama que muestra los diferentes tipos de bucles en JavaScript: for, while y do-while.'
        };
    }

    generateFunctionsExample() {
        return {
            svg: this.generate('funciones'),
            description: 'Diagrama que explica los conceptos básicos de funciones en JavaScript, incluyendo declaración, parámetros y retorno.'
        };
    }

    generateArraysExample() {
        return {
            svg: this.generate('arrays'),
            description: 'Diagrama que muestra los conceptos básicos de arrays y objetos en JavaScript, incluyendo creación, acceso y métodos comunes.'
        };
    }

    generateStringsExample() {
        return {
            svg: this.generate('strings'),
            description: 'Diagrama que explica los métodos básicos para manipular strings en JavaScript.'
        };
    }
}

module.exports = BeginnerGenerator;
