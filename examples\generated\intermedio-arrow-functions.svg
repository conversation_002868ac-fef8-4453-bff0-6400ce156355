
<svg width="1000" height="700" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Arrow functions</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
        <!-- Prerequisitos -->
        
        <g class="box-group">
            <rect x="50" y="80" width="900" height="60" class="warning-box" rx="8" />
            <text x="65" y="105" class="subtitle">Prerequisitos</text>
            <text x="65" y="130" class="text">Funciones básicas, this keyword, métodos de array</text>
        </g>
        
        <!-- Definición -->
        
        <g class="box-group">
            <rect x="50" y="160" width="900" height="80" class="concept-box" rx="8" />
            <text x="65" y="185" class="subtitle">Definición</text>
            <text x="65" y="210" class="text">Las arrow functions son una forma más concisa de escribir funciones. Tienen un comportamiento especial con el contexto (this) y no pueden ser usadas como constructores.</text>
        </g>
        
        <!-- Sintaxis básica -->
        
        <g class="box-group">
            <rect x="50" y="260" width="430" height="150" class="concept-box" rx="8" />
            <text x="65" y="285" class="subtitle">Sintaxis básica</text>
            <text x="65" y="310" class="text">Función tradicional:
function(param) { return param * 2; }

Arrow function:
(param) => param * 2

Con múltiples parámetros:
(a, b) => a + b

Sin parámetros:
() => "Hola mundo"</text>
        </g>
        
        <!-- Sintaxis con bloque -->
        
        <g class="box-group">
            <rect x="520" y="260" width="430" height="150" class="concept-box" rx="8" />
            <text x="535" y="285" class="subtitle">Con bloque de código</text>
            <text x="535" y="310" class="text">Arrow function con {}:
(param) => {
  const resultado = param * 2;
  return resultado;
}

Sin {}, return implícito:
(param) => param * 2</text>
        </g>
        
        <!-- Diferencias con this -->
        
        <g class="code-group">
            <rect x="50" y="430" width="430" height="120" class="code-box" rx="4" />
            <text x="65" y="455" class="code">// Función tradicional</text>
<text x="65" y="473" class="code">const obj = {</text>
<text x="65" y="491" class="code">  nombre: &quot;Ana&quot;,</text>
<text x="65" y="509" class="code">  saludar: function() {</text>
<text x="65" y="527" class="code">    console.log(&quot;Hola &quot; + this.nombre);</text>
<text x="65" y="545" class="code">  }</text>
<text x="65" y="563" class="code">};</text>
<text x="65" y="581" class="code">obj.saludar(); // &quot;Hola Ana&quot;</text>

        </g>
        
        <!-- Arrow function y this -->
        
        <g class="code-group">
            <rect x="520" y="430" width="430" height="120" class="code-box" rx="4" />
            <text x="535" y="455" class="code">// Arrow function</text>
<text x="535" y="473" class="code">const obj = {</text>
<text x="535" y="491" class="code">  nombre: &quot;Ana&quot;,</text>
<text x="535" y="509" class="code">  saludar: () =&gt; {</text>
<text x="535" y="527" class="code">    console.log(&quot;Hola &quot; + this.nombre);</text>
<text x="535" y="545" class="code">  }</text>
<text x="535" y="563" class="code">};</text>
<text x="535" y="581" class="code">obj.saludar(); // &quot;Hola undefined&quot;</text>

        </g>
        
        <!-- Casos de uso -->
        
        <g class="box-group">
            <rect x="50" y="570" width="900" height="80" class="example-box" rx="8" />
            <text x="65" y="595" class="subtitle">Casos de uso comunes</text>
            <text x="65" y="620" class="text">Callbacks en métodos de array (map, filter, reduce). Event listeners. Funciones cortas y simples. Cuando no necesitas tu propio contexto this.</text>
        </g>
        
    </g>
</svg>