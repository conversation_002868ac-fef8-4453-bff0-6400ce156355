
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
        <marker id="arrowhead-normal" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
        </marker>
        <marker id="arrowhead-true" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
        </marker>
        <marker id="arrowhead-false" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#ea580c" />
        </marker>
        <marker id="arrowhead-process" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#2563eb" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Análisis de árbol de sintaxis (AST)</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
            <g class="ast-node" data-level="0">
                <rect x="340" y="100" width="120" height="40" rx="6"
                      fill="#2563eb" fill-opacity="0.2" stroke="#2563eb" stroke-width="2"/>
                <text x="400" y="125" text-anchor="middle" class="small-text" fill="#2563eb">
                    Program
                </text>
            </g><line x1="400" y1="140" x2="390" y2="180" 
                           stroke="#6b7280" stroke-width="1"/>
            <g class="ast-node" data-level="1">
                <rect x="330" y="180" width="120" height="40" rx="6"
                      fill="#0ea5e9" fill-opacity="0.2" stroke="#0ea5e9" stroke-width="2"/>
                <text x="390" y="205" text-anchor="middle" class="small-text" fill="#0ea5e9">
                    VariableDeclaration
                </text>
            </g><line x1="390" y1="220" x2="380" y2="260" 
                           stroke="#6b7280" stroke-width="1"/>
            <g class="ast-node" data-level="2">
                <rect x="320" y="260" width="120" height="40" rx="6"
                      fill="#6b7280" fill-opacity="0.2" stroke="#6b7280" stroke-width="2"/>
                <text x="380" y="285" text-anchor="middle" class="small-text" fill="#6b7280">
                    VariableDeclarator
                </text>
            </g><line x1="380" y1="300" x2="300" y2="340" 
                           stroke="#6b7280" stroke-width="1"/>
            <g class="ast-node" data-level="3">
                <rect x="240" y="340" width="120" height="40" rx="6"
                      fill="#8b5cf6" fill-opacity="0.2" stroke="#8b5cf6" stroke-width="2"/>
                <text x="300" y="365" text-anchor="middle" class="small-text" fill="#8b5cf6">
                    Identifier
                </text>
            </g><text x="300" y="395" text-anchor="middle" class="small-text" 
                       fill="#6b7280">x</text><line x1="380" y1="300" x2="440" y2="340" 
                           stroke="#6b7280" stroke-width="1"/>
            <g class="ast-node" data-level="3">
                <rect x="380" y="340" width="120" height="40" rx="6"
                      fill="#ea580c" fill-opacity="0.2" stroke="#ea580c" stroke-width="2"/>
                <text x="440" y="365" text-anchor="middle" class="small-text" fill="#ea580c">
                    BinaryExpression
                </text>
            </g><line x1="440" y1="380" x2="290" y2="420" 
                           stroke="#6b7280" stroke-width="1"/>
            <g class="ast-node" data-level="4">
                <rect x="230" y="420" width="120" height="40" rx="6"
                      fill="#ec4899" fill-opacity="0.2" stroke="#ec4899" stroke-width="2"/>
                <text x="290" y="445" text-anchor="middle" class="small-text" fill="#ec4899">
                    Literal
                </text>
            </g><text x="290" y="475" text-anchor="middle" class="small-text" 
                       fill="#6b7280">5</text><line x1="440" y1="380" x2="430" y2="420" 
                           stroke="#6b7280" stroke-width="1"/>
            <g class="ast-node" data-level="4">
                <rect x="370" y="420" width="120" height="40" rx="6"
                      fill="#6b7280" fill-opacity="0.2" stroke="#6b7280" stroke-width="2"/>
                <text x="430" y="445" text-anchor="middle" class="small-text" fill="#6b7280">
                    Operator
                </text>
            </g><text x="430" y="475" text-anchor="middle" class="small-text" 
                       fill="#6b7280">+</text><line x1="440" y1="380" x2="570" y2="420" 
                           stroke="#6b7280" stroke-width="1"/>
            <g class="ast-node" data-level="4">
                <rect x="510" y="420" width="120" height="40" rx="6"
                      fill="#ec4899" fill-opacity="0.2" stroke="#ec4899" stroke-width="2"/>
                <text x="570" y="445" text-anchor="middle" class="small-text" fill="#ec4899">
                    Literal
                </text>
            </g><text x="570" y="475" text-anchor="middle" class="small-text" 
                       fill="#6b7280">3</text>
        <g class="code-group">
            <rect x="50" y="100" width="300" height="100" class="code-box" rx="4" />
            <text x="65" y="125" class="code">const x = 5 + 3;</text>
<text x="65" y="141" class="code"></text>
<text x="65" y="157" class="code">// Este código se parsea en</text>
<text x="65" y="173" class="code">// el árbol mostrado →</text>

        </g>
        <g class="box-group">
            <rect x="50" y="220" width="300" height="200" class="concept-box" rx="8" />
            <text x="65" y="245" class="subtitle">Análisis AST</text>
            <text x="65" y="270" class="text">El Abstract Syntax Tree (AST)</text>
<text x="65" y="288" class="text">representa la estructura</text>
<text x="65" y="306" class="text">sintáctica del código. Cada nodo</text>
<text x="65" y="324" class="text">representa una construcción del</text>
<text x="65" y="342" class="text">lenguaje. Los parsers convierten</text>
<text x="65" y="360" class="text">código fuente en AST para</text>
<text x="65" y="378" class="text">análisis y transformación.</text>

        </g>
    </g>
</svg>