
<svg width="1000" height="700" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
        <marker id="arrowhead-normal" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
        </marker>
        <marker id="arrowhead-true" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
        </marker>
        <marker id="arrowhead-false" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#ea580c" />
        </marker>
        <marker id="arrowhead-process" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#2563eb" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Flujo condicional y bucles</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
        <g class="connection">
            <path d="M 410 110 L 410 150" stroke="#6b7280" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-normal)"/>
            
        </g>
        <g class="connection">
            <path d="M 410 210 L 410 250" stroke="#6b7280" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-normal)"/>
            
        </g>
        <g class="connection">
            <path d="M 410 310 L 260 350" stroke="#10b981" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-true)"/>
            <text x="335" y="325" text-anchor="middle" 
                           class="small-text" fill="#10b981">Sí</text>
        </g>
        <g class="connection">
            <path d="M 410 310 L 560 350" stroke="#ea580c" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-false)"/>
            <text x="485" y="325" text-anchor="middle" 
                           class="small-text" fill="#ea580c">No</text>
        </g>
        <g class="connection">
            <path d="M 260 410 L 260 450" stroke="#6b7280" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-normal)"/>
            
        </g>
        <g class="connection">
            <path d="M 260 510 Q 260 380 410 250" stroke="#6b7280" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-normal)"/>
            
        </g>
        <g class="flow-node" data-id="start">
            <ellipse cx="410" cy="80" rx="60" ry="30" 
                         fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="410" y="80" 
                            text-anchor="middle" class="small-text" fill="#10b981">Inicio</text>

        </g>
        <g class="flow-node" data-id="init">
            <rect x="350" y="150" width="120" height="60" rx="8" 
                         fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="410" y="180" 
                            text-anchor="middle" class="small-text" fill="#2563eb">i = 0</text>

        </g>
        <g class="flow-node" data-id="condition">
            <polygon points="410,250 470,280 410,310 350,280" 
                         fill="#0ea5e9" fill-opacity="0.1" stroke="#0ea5e9" stroke-width="2"/>
            <text x="410" y="280" 
                            text-anchor="middle" class="small-text" fill="#0ea5e9">i < 5?</text>

        </g>
        <g class="flow-node" data-id="body">
            <rect x="200" y="350" width="120" height="60" rx="8" 
                         fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="260" y="380" 
                            text-anchor="middle" class="small-text" fill="#2563eb">console.log(i)</text>

        </g>
        <g class="flow-node" data-id="increment">
            <rect x="200" y="450" width="120" height="60" rx="8" 
                         fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="260" y="480" 
                            text-anchor="middle" class="small-text" fill="#2563eb">i++</text>

        </g>
        <g class="flow-node" data-id="end">
            <ellipse cx="560" cy="380" rx="60" ry="30" 
                         fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="560" y="380" 
                            text-anchor="middle" class="small-text" fill="#ea580c">Fin</text>

        </g>
        <path d="M 200 470 Q 100 470 100 250 Q 100 230 330 250"
              stroke="#2563eb" stroke-width="2" fill="none"
              marker-end="url(#arrowhead-process)" stroke-dasharray="5,5"/>
        <text x="100" y="360" class="small-text" fill="#2563eb">Bucle</text>
    </g>
</svg>