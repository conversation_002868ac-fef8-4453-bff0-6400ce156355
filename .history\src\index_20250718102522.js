const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const path = require('path');
const fs = require('fs');

const DiagramGenerator = require('./generators/DiagramGenerator');
const ExportUtils = require('./utils/ExportUtils');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../dist')));

// Instancias de utilidades
const diagramGenerator = new DiagramGenerator();
const exportUtils = new ExportUtils();

// Rutas API
app.get('/api/topics/:level', (req, res) => {
    try {
        const { level } = req.params;
        const topics = diagramGenerator.getTopicsByLevel(level);
        res.json({ success: true, topics });
    } catch (error) {
        res.status(400).json({ success: false, error: error.message });
    }
});

app.post('/api/generate', (req, res) => {
    try {
        const { topic, level, options = {} } = req.body;
        
        if (!topic || !level) {
            return res.status(400).json({ 
                success: false, 
                error: 'Topic and level are required' 
            });
        }

        const svg = diagramGenerator.generateDiagram(topic, level, options);
        res.json({ success: true, svg });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.get('/api/example/:level/:topic', (req, res) => {
    try {
        const { level, topic } = req.params;
        const example = diagramGenerator.getExample(level, topic);
        res.json({ success: true, example });
    } catch (error) {
        res.status(404).json({ success: false, error: error.message });
    }
});

// Ruta para exportar diagramas
app.post('/api/export', async (req, res) => {
    try {
        const { svg, format, filename } = req.body;

        if (!svg || !format || !filename) {
            return res.status(400).json({
                success: false,
                error: 'SVG, format y filename son requeridos'
            });
        }

        const outputPath = await exportUtils.exportDiagram(svg, format, filename);
        res.json({ success: true, outputPath });
    } catch (error) {
        console.error('Error en exportación:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Ruta para descargar archivos exportados
app.get('/api/download/:filename', (req, res) => {
    try {
        const { filename } = req.params;
        const filePath = path.join(__dirname, '../exports', filename);

        if (!fs.existsSync(filePath)) {
            return res.status(404).json({ success: false, error: 'Archivo no encontrado' });
        }

        res.download(filePath, (err) => {
            if (err) {
                console.error('Error descargando archivo:', err);
                res.status(500).json({ success: false, error: 'Error descargando archivo' });
            }
        });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// Ruta para obtener información de formatos
app.get('/api/formats', (req, res) => {
    try {
        const formats = exportUtils.getFormatInfo();
        res.json({ success: true, formats });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// Ruta para exportación en lote
app.post('/api/export/batch', async (req, res) => {
    try {
        const { diagrams, format } = req.body;

        if (!diagrams || !Array.isArray(diagrams) || !format) {
            return res.status(400).json({
                success: false,
                error: 'Diagrams (array) y format son requeridos'
            });
        }

        const results = await exportUtils.exportBatch(diagrams, format);
        res.json({ success: true, results });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// Ruta principal
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../dist/index.html'));
});

// Manejo de errores
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ success: false, error: 'Internal server error' });
});

// Iniciar servidor
app.listen(PORT, () => {
    console.log(`🚀 JavaScript Diagram Generator running on port ${PORT}`);
    console.log(`📊 Access the web interface at http://localhost:${PORT}`);
});

module.exports = app;
