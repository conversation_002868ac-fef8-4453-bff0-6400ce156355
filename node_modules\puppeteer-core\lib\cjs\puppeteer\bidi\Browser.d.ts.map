{"version": 3, "file": "Browser.d.ts", "sourceRoot": "", "sources": ["../../../../src/bidi/Browser.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAC,YAAY,EAAC,MAAM,oBAAoB,CAAC;AAErD,OAAO,KAAK,IAAI,MAAM,4CAA4C,CAAC;AAGnE,OAAO,EACL,OAAO,EAEP,KAAK,oBAAoB,EACzB,KAAK,qBAAqB,EAC1B,KAAK,SAAS,EACf,MAAM,mBAAmB,CAAC;AAE3B,OAAO,KAAK,EAAC,IAAI,EAAC,MAAM,gBAAgB,CAAC;AACzC,OAAO,KAAK,EAAC,MAAM,EAAC,MAAM,kBAAkB,CAAC;AAC7C,OAAO,KAAK,EAAC,UAAU,IAAI,aAAa,EAAC,MAAM,sBAAsB,CAAC;AACtE,OAAO,KAAK,EAAC,8BAA8B,EAAC,MAAM,6BAA6B,CAAC;AAIhF,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,uBAAuB,CAAC;AAGpD,OAAO,EAAC,kBAAkB,EAAC,MAAM,qBAAqB,CAAC;AACvD,OAAO,KAAK,EAAC,cAAc,EAAC,MAAM,iBAAiB,CAAC;AAIpD,OAAO,EAAC,iBAAiB,EAAC,MAAM,aAAa,CAAC;AAE9C;;GAEG;AACH,MAAM,WAAW,kBAAkB;IACjC,OAAO,CAAC,EAAE,YAAY,CAAC;IACvB,aAAa,CAAC,EAAE,oBAAoB,CAAC;IACrC,UAAU,EAAE,cAAc,CAAC;IAC3B,aAAa,CAAC,EAAE,aAAa,CAAC;IAC9B,eAAe,EAAE,QAAQ,GAAG,IAAI,CAAC;IACjC,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAC9B,YAAY,CAAC,EAAE,8BAA8B,CAAC;IAC9C,cAAc,EAAE,OAAO,CAAC;CACzB;AAED;;GAEG;AACH,qBAAa,WAAY,SAAQ,OAAO;;IACtC,QAAQ,CAAC,QAAQ,mBAAmB;IAEpC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,CAMrD;IACF,MAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAWvD;WAEW,MAAM,CAAC,IAAI,EAAE,kBAAkB,GAAG,OAAO,CAAC,WAAW,CAAC;IAgEnE,OAAO;IAiCP,IAAI,YAAY,IAAI,OAAO,CAE1B;IAED,IAAI,aAAa,IAAI,aAAa,GAAG,SAAS,CAE7C;IAEc,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC;IAgC3C,IAAI,UAAU,IAAI,cAAc,CAG/B;IAEQ,UAAU,IAAI,MAAM;IAId,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAgBrC,IAAa,SAAS,IAAI,OAAO,CAEhC;IAEQ,OAAO,IAAI,YAAY,GAAG,IAAI;IAIxB,oBAAoB,CACjC,OAAO,GAAE,qBAA0B,GAClC,OAAO,CAAC,kBAAkB,CAAC;IAKf,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;IAIhC,eAAe,IAAI,kBAAkB,EAAE;IAMvC,qBAAqB,IAAI,kBAAkB;IAI3C,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAIxB,gBAAgB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAIzC,kBAAkB,CAAC,EAAE,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAInD,OAAO,IAAI,MAAM,EAAE;IASnB,MAAM,IAAI,iBAAiB;IAIrB,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAW1C,IAAa,SAAS,IAAI,SAAS,CAIlC;IAEQ,gBAAgB,IAAI,OAAO;CAGrC"}