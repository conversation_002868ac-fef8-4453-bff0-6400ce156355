const BaseGenerator = require('./BaseGenerator');

class AdvancedGenerator extends BaseGenerator {
    constructor() {
        super();
        
        // Definir temas disponibles para nivel avanzado
        this.topics = {
            'closures': 'Closures y lexical scope',
            'prototypes': 'Prototypes y herencia',
            'async-await': 'Async/await y manejo de errores',
            'modules': 'Módulos ES6',
            'design-patterns': 'Patrones de diseño',
            'performance': 'Performance optimization',
            'testing': 'Testing básico',
            'generators': 'Generators y iteradores',
            'proxy-reflect': 'Proxy y Reflect',
            'symbols': 'Symbols',
            'weakmap-weakset': 'WeakMap y WeakSet',
            'service-workers': 'Service Workers',
            'web-apis': 'Web APIs avanzadas',
            'functional-programming': 'Programación funcional',
            'memory-leaks': 'Memory leaks y optimización',
            'bundlers': 'Bundlers y herramientas',
            'typescript-basics': 'TypeScript básico'
        };
        
        // Eje<PERSON>los predefinidos
        this.examples = {
            'closures': this.generateClosuresExample(),
            'prototypes': this.generatePrototypesExample(),
            'async-await': this.generateAsyncAwaitExample(),
            'modules': this.generateModulesExample(),
            'design-patterns': this.generateDesignPatternsExample(),
            'performance': this.generatePerformanceExample(),
            'testing': this.generateTestingExample()
        };
    }

    /**
     * Genera un diagrama SVG para un tema de nivel avanzado
     * @param {string} topic - Tema a generar
     * @param {object} options - Opciones adicionales
     * @returns {string} - SVG generado
     */
    generate(topic, options = {}) {
        const normalizedTopic = topic.toLowerCase();
        
        if (!this.hasTopicSupport(normalizedTopic)) {
            throw new Error(`El tema "${topic}" no está soportado para nivel avanzado`);
        }
        
        // Obtener el título completo del tema
        const title = this.topics[normalizedTopic];
        
        // Generar el SVG base con dimensiones más grandes para contenido complejo
        const svgOptions = { width: 1200, height: 800, ...options };
        let svg = this.generateBaseSVG(title, svgOptions);
        
        // Generar el contenido específico según el tema
        let content = '';
        switch (normalizedTopic) {
            case 'closures':
                content = this.generateClosuresContent();
                break;
            case 'prototypes':
                content = this.generatePrototypesContent();
                break;
            case 'async-await':
                content = this.generateAsyncAwaitContent();
                break;
            case 'modules':
                content = this.generateModulesContent();
                break;
            case 'design-patterns':
                content = this.generateDesignPatternsContent();
                break;
            case 'performance':
                content = this.generatePerformanceContent();
                break;
            case 'testing':
                content = this.generateTestingContent();
                break;
            default:
                content = '<text x="600" y="400" text-anchor="middle" class="text">Contenido no disponible</text>';
        }
        
        // Insertar el contenido en el SVG
        svg = svg.replace('<!-- El contenido específico se añadirá aquí -->', content);
        
        return svg;
    }

    /**
     * Genera el contenido para el tema de closures
     * @returns {string} - SVG del contenido
     */
    generateClosuresContent() {
        return `
        <!-- Conceptos fundamentales -->
        ${this.createBox(50, 80, 1100, 80, 'Conceptos fundamentales requeridos', 'Scope, Lexical scope, Funciones como first-class objects, Execution context, Call stack', 'warning')}
        
        <!-- Definición técnica -->
        ${this.createBox(50, 180, 530, 120, 'Definición técnica', 'Un closure es la combinación de una función y el entorno léxico en el que fue declarada. Permite que una función acceda a variables de su scope externo incluso después de que la función externa haya terminado de ejecutarse.', 'concept')}
        
        <!-- Scope chain -->
        ${this.createBox(620, 180, 530, 120, 'Scope Chain', 'JavaScript busca variables siguiendo la cadena de scope:\n1. Scope local de la función\n2. Scope de la función contenedora\n3. Scope global\n\nLos closures mantienen esta cadena activa.', 'concept')}
        
        <!-- Ejemplo básico -->
        ${this.createCodeBox(50, 320, 530, 150, 'function externa(x) {\n  // Variable en scope externo\n  \n  function interna(y) {\n    return x + y; // Accede a x del scope externo\n  }\n  \n  return interna;\n}\n\nconst miFuncion = externa(10);\nconsole.log(miFuncion(5)); // 15')}
        
        <!-- Caso de uso: Módulo -->
        ${this.createCodeBox(620, 320, 530, 150, 'function crearContador() {\n  let count = 0; // Variable privada\n  \n  return {\n    incrementar: () => ++count,\n    decrementar: () => --count,\n    obtener: () => count\n  };\n}\n\nconst contador = crearContador();\nconsole.log(contador.obtener()); // 0')}
        
        <!-- Problemas comunes -->
        ${this.createBox(50, 490, 530, 100, 'Problemas comunes', 'Memory leaks: Los closures mantienen referencias\nBucles con closures: Usar let en lugar de var\nRendimiento: Evitar closures innecesarios en funciones frecuentes', 'warning')}
        
        <!-- Casos de uso prácticos -->
        ${this.createBox(620, 490, 530, 100, 'Casos de uso prácticos', 'Módulos y encapsulación\nCallbacks y event handlers\nFunciones de orden superior\nFactory functions\nCurrying y partial application', 'example')}
        
        <!-- Debugging -->
        ${this.createBox(50, 610, 1100, 80, 'Debugging y troubleshooting', 'Usar debugger para inspeccionar scope. Verificar referencias con DevTools. Cuidado con variables que cambian en bucles. Liberar referencias para evitar memory leaks.', 'example')}
        `;
    }

    // Métodos para generar contenido de otros temas (implementación básica)
    generatePrototypesContent() {
        return `<!-- Contenido para prototypes -->`;
    }

    generateAsyncAwaitContent() {
        return `<!-- Contenido para async/await -->`;
    }

    generateModulesContent() {
        return `<!-- Contenido para módulos -->`;
    }

    generateDesignPatternsContent() {
        return `<!-- Contenido para patrones de diseño -->`;
    }

    generatePerformanceContent() {
        return `<!-- Contenido para performance -->`;
    }

    generateTestingContent() {
        return `<!-- Contenido para testing -->`;
    }

    // Métodos para generar ejemplos
    generateClosuresExample() {
        return {
            svg: this.generate('closures'),
            description: 'Diagrama avanzado que explica los closures en JavaScript, incluyendo scope chain, casos de uso prácticos y problemas comunes como memory leaks.'
        };
    }

    generatePrototypesExample() {
        return {
            svg: this.generate('prototypes'),
            description: 'Diagrama que muestra el sistema de prototipos de JavaScript y cómo implementar herencia.'
        };
    }

    generateAsyncAwaitExample() {
        return {
            svg: this.generate('async-await'),
            description: 'Diagrama que explica async/await y el manejo avanzado de errores en operaciones asíncronas.'
        };
    }

    generateModulesExample() {
        return {
            svg: this.generate('modules'),
            description: 'Diagrama que muestra cómo usar módulos ES6 para organizar y estructurar aplicaciones JavaScript.'
        };
    }

    generateDesignPatternsExample() {
        return {
            svg: this.generate('design-patterns'),
            description: 'Diagrama que presenta patrones de diseño comunes en JavaScript como Observer, Factory y Module.'
        };
    }

    generatePerformanceExample() {
        return {
            svg: this.generate('performance'),
            description: 'Diagrama que explica técnicas de optimización de rendimiento en JavaScript.'
        };
    }

    generateTestingExample() {
        return {
            svg: this.generate('testing'),
            description: 'Diagrama que introduce conceptos básicos de testing en JavaScript incluyendo unit tests y mocking.'
        };
    }
}

module.exports = AdvancedGenerator;
