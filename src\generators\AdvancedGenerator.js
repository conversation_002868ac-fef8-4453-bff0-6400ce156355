const BaseGenerator = require('./BaseGenerator');

class AdvancedGenerator extends BaseGenerator {
    constructor() {
        super();
        
        // Definir temas disponibles para nivel avanzado
        this.topics = {
            'closures': 'Closures y lexical scope',
            'prototypes': 'Prototypes y herencia',
            'async-await': 'Async/await y manejo de errores',
            'modules': 'Módulos ES6',
            'design-patterns': 'Patrones de diseño',
            'performance': 'Performance optimization',
            'testing': 'Testing básico',
            'generators': 'Generators y iteradores',
            'proxy-reflect': 'Proxy y Reflect',
            'symbols': 'Symbols',
            'weakmap-weakset': 'WeakMap y WeakSet',
            'service-workers': 'Service Workers',
            'web-apis': 'Web APIs avanzadas',
            'functional-programming': 'Programación funcional',
            'memory-leaks': 'Memory leaks y optimización',
            'bundlers': 'Bundlers y herramientas',
            'typescript-basics': 'TypeScript básico',
            'ast-analysis': 'Análisis de árbol de sintaxis (AST)',
            'scope-chain': 'Cadena de scope visual',
            'call-graph': 'Grafo de llamadas de funciones',
            'execution-context': 'Contexto de ejecución visual'
        };
        
        // Ejemplos predefinidos
        this.examples = {
            'closures': this.generateClosuresExample(),
            'prototypes': this.generatePrototypesExample(),
            'async-await': this.generateAsyncAwaitExample(),
            'modules': this.generateModulesExample(),
            'design-patterns': this.generateDesignPatternsExample(),
            'performance': this.generatePerformanceExample(),
            'testing': this.generateTestingExample()
        };
    }

    /**
     * Genera un diagrama SVG para un tema de nivel avanzado
     * @param {string} topic - Tema a generar
     * @param {object} options - Opciones adicionales
     * @returns {string} - SVG generado
     */
    generate(topic, options = {}) {
        const normalizedTopic = topic.toLowerCase();
        
        if (!this.hasTopicSupport(normalizedTopic)) {
            throw new Error(`El tema "${topic}" no está soportado para nivel avanzado`);
        }
        
        // Obtener el título completo del tema
        const title = this.topics[normalizedTopic];
        
        // Generar el SVG base con dimensiones más grandes para contenido complejo
        const svgOptions = { width: 1200, height: 800, ...options };
        let svg = this.generateBaseSVG(title, svgOptions);
        
        // Generar el contenido específico según el tema
        let content = '';
        switch (normalizedTopic) {
            case 'closures':
                content = this.generateClosuresContent();
                break;
            case 'prototypes':
                content = this.generatePrototypesContent();
                break;
            case 'async-await':
                content = this.generateAsyncAwaitContent();
                break;
            case 'modules':
                content = this.generateModulesContent();
                break;
            case 'design-patterns':
                content = this.generateDesignPatternsContent();
                break;
            case 'performance':
                content = this.generatePerformanceContent();
                break;
            case 'testing':
                content = this.generateTestingContent();
                break;
            case 'ast-analysis':
                content = this.generateASTAnalysisContent();
                break;
            case 'scope-chain':
                content = this.generateScopeChainContent();
                break;
            case 'call-graph':
                content = this.generateCallGraphContent();
                break;
            case 'execution-context':
                content = this.generateExecutionContextContent();
                break;
            default:
                content = '<text x="600" y="400" text-anchor="middle" class="text">Contenido no disponible</text>';
        }
        
        // Insertar el contenido en el SVG
        svg = svg.replace('<!-- El contenido específico se añadirá aquí -->', content);
        
        return svg;
    }

    /**
     * Genera el contenido para el tema de closures
     * @returns {string} - SVG del contenido
     */
    generateClosuresContent() {
        return `
        <!-- Conceptos fundamentales -->
        ${this.createBox(50, 80, 1100, 80, 'Conceptos fundamentales requeridos', 'Scope, Lexical scope, Funciones como first-class objects, Execution context, Call stack', 'warning')}
        
        <!-- Definición técnica -->
        ${this.createBox(50, 180, 530, 120, 'Definición técnica', 'Un closure es la combinación de una función y el entorno léxico en el que fue declarada. Permite que una función acceda a variables de su scope externo incluso después de que la función externa haya terminado de ejecutarse.', 'concept')}
        
        <!-- Scope chain -->
        ${this.createBox(620, 180, 530, 120, 'Scope Chain', 'JavaScript busca variables siguiendo la cadena de scope:\n1. Scope local de la función\n2. Scope de la función contenedora\n3. Scope global\n\nLos closures mantienen esta cadena activa.', 'concept')}
        
        <!-- Ejemplo básico -->
        ${this.createCodeBox(50, 320, 530, 150, 'function externa(x) {\n  // Variable en scope externo\n  \n  function interna(y) {\n    return x + y; // Accede a x del scope externo\n  }\n  \n  return interna;\n}\n\nconst miFuncion = externa(10);\nconsole.log(miFuncion(5)); // 15')}
        
        <!-- Caso de uso: Módulo -->
        ${this.createCodeBox(620, 320, 530, 150, 'function crearContador() {\n  let count = 0; // Variable privada\n  \n  return {\n    incrementar: () => ++count,\n    decrementar: () => --count,\n    obtener: () => count\n  };\n}\n\nconst contador = crearContador();\nconsole.log(contador.obtener()); // 0')}
        
        <!-- Problemas comunes -->
        ${this.createBox(50, 490, 530, 100, 'Problemas comunes', 'Memory leaks: Los closures mantienen referencias\nBucles con closures: Usar let en lugar de var\nRendimiento: Evitar closures innecesarios en funciones frecuentes', 'warning')}
        
        <!-- Casos de uso prácticos -->
        ${this.createBox(620, 490, 530, 100, 'Casos de uso prácticos', 'Módulos y encapsulación\nCallbacks y event handlers\nFunciones de orden superior\nFactory functions\nCurrying y partial application', 'example')}
        
        <!-- Debugging -->
        ${this.createBox(50, 610, 1100, 80, 'Debugging y troubleshooting', 'Usar debugger para inspeccionar scope. Verificar referencias con DevTools. Cuidado con variables que cambian en bucles. Liberar referencias para evitar memory leaks.', 'example')}
        `;
    }

    // Métodos para generar contenido de otros temas (implementación básica)
    generatePrototypesContent() {
        return `<!-- Contenido para prototypes -->`;
    }

    generateAsyncAwaitContent() {
        return `<!-- Contenido para async/await -->`;
    }

    generateModulesContent() {
        return `<!-- Contenido para módulos -->`;
    }

    generateDesignPatternsContent() {
        return `<!-- Contenido para patrones de diseño -->`;
    }

    generatePerformanceContent() {
        return `<!-- Contenido para performance -->`;
    }

    generateTestingContent() {
        return `<!-- Contenido para testing -->`;
    }

    /**
     * Genera análisis de árbol de sintaxis abstracta (AST)
     * @returns {string} - SVG del contenido
     */
    generateASTAnalysisContent() {
        // Crear AST para: const x = 5 + 3;
        const astNode = {
            type: 'Program',
            children: [
                {
                    type: 'VariableDeclaration',
                    children: [
                        {
                            type: 'VariableDeclarator',
                            children: [
                                { type: 'Identifier', value: 'x' },
                                {
                                    type: 'BinaryExpression',
                                    children: [
                                        { type: 'Literal', value: '5' },
                                        { type: 'Operator', value: '+' },
                                        { type: 'Literal', value: '3' }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        };

        const syntaxTree = this.visualElements.createSyntaxTree(astNode, {
            startX: 400,
            startY: 100,
            levelHeight: 80,
            nodeWidth: 120
        });

        // Código de ejemplo
        const codeExample = this.createCodeBox(50, 100, 300, 100,
            'const x = 5 + 3;\n\n// Este código se parsea en\n// el árbol mostrado →'
        );

        // Explicación
        const explanation = this.createBox(50, 220, 300, 200,
            'Análisis AST',
            'El Abstract Syntax Tree (AST) representa la estructura sintáctica del código. Cada nodo representa una construcción del lenguaje. Los parsers convierten código fuente en AST para análisis y transformación.',
            'concept'
        );

        return syntaxTree + codeExample + explanation;
    }

    /**
     * Genera diagrama de cadena de scope
     * @returns {string} - SVG del contenido
     */
    generateScopeChainContent() {
        const scopes = [
            {
                name: 'Global',
                level: 0,
                variables: [
                    { name: 'globalVar', type: 'string' },
                    { name: 'outerFunction', type: 'function' }
                ]
            },
            {
                name: 'Function',
                level: 1,
                parent: 0,
                variables: [
                    { name: 'localVar', type: 'number' },
                    { name: 'innerFunction', type: 'function' }
                ]
            },
            {
                name: 'Block',
                level: 2,
                parent: 1,
                variables: [
                    { name: 'blockVar', type: 'boolean' },
                    { name: 'tempVar', type: 'object' }
                ]
            }
        ];

        const scopeAnalysis = this.visualElements.createScopeAnalysis(scopes, {
            startX: 50,
            startY: 100,
            scopeWidth: 250,
            scopeHeight: 120
        });

        // Código de ejemplo
        const codeExample = this.createCodeBox(400, 100, 350, 200,
            'let globalVar = "global";\n\nfunction outerFunction() {\n  let localVar = 42;\n  \n  function innerFunction() {\n    if (true) {\n      let blockVar = true;\n      // Acceso a todas las variables\n      console.log(globalVar, localVar, blockVar);\n    }\n  }\n}'
        );

        // Explicación de lookup
        const lookupExplanation = this.createBox(400, 320, 350, 150,
            'Lookup de Variables',
            'JavaScript busca variables siguiendo la cadena de scope: 1) Scope actual, 2) Scope padre, 3) Scope global. Si no encuentra la variable, lanza ReferenceError.',
            'example'
        );

        return scopeAnalysis + codeExample + lookupExplanation;
    }

    /**
     * Genera grafo de llamadas de funciones
     * @returns {string} - SVG del contenido
     */
    generateCallGraphContent() {
        const functions = [
            { name: 'main', type: 'main', calls: ['processData', 'validateInput'] },
            { name: 'processData', calls: ['formatData', 'saveData'] },
            { name: 'validateInput', calls: ['checkType', 'checkRange'] },
            { name: 'formatData', calls: [] },
            { name: 'saveData', calls: ['logAction'] },
            { name: 'checkType', calls: [] },
            { name: 'checkRange', calls: [] },
            { name: 'logAction', calls: [] }
        ];

        const callGraph = this.visualElements.createCallGraph(functions, {
            width: 700,
            height: 500
        });

        // Código de ejemplo
        const codeExample = this.createCodeBox(50, 520, 700, 120,
            'function main() { processData(); validateInput(); }\nfunction processData() { formatData(); saveData(); }\nfunction validateInput() { checkType(); checkRange(); }\n// ... más funciones'
        );

        // Explicación
        const explanation = this.createBox(750, 100, 300, 150,
            'Grafo de Llamadas',
            'Muestra las relaciones entre funciones. Útil para: análisis de dependencias, detección de funciones no utilizadas, optimización de código.',
            'concept'
        );

        return callGraph + codeExample + explanation;
    }

    /**
     * Genera diagrama de contexto de ejecución
     * @returns {string} - SVG del contenido
     */
    generateExecutionContextContent() {
        // Crear stack de contextos de ejecución
        const contexts = [
            { name: 'Global Context', level: 0, active: false },
            { name: 'outerFunction()', level: 1, active: false },
            { name: 'innerFunction()', level: 2, active: true }
        ];

        let svg = '';
        const startX = 100;
        const startY = 100;
        const contextHeight = 80;
        const contextWidth = 200;

        // Dibujar stack de contextos
        contexts.forEach((context, index) => {
            const y = startY + index * (contextHeight + 10);
            const color = context.active ? this.colors.primary : this.colors.gray;
            const opacity = context.active ? '0.2' : '0.1';

            svg += `
            <rect x="${startX}" y="${y}" width="${contextWidth}" height="${contextHeight}"
                  fill="${color}" fill-opacity="${opacity}" stroke="${color}" stroke-width="2" rx="8"/>
            <text x="${startX + 10}" y="${y + 25}" class="subtitle" fill="${color}">
                ${context.name}
            </text>
            <text x="${startX + 10}" y="${y + 45}" class="small-text" fill="${this.colors.text}">
                Variables: ${context.level === 0 ? 'globalVar' : context.level === 1 ? 'localVar' : 'blockVar'}
            </text>
            <text x="${startX + 10}" y="${y + 65}" class="small-text" fill="${this.colors.text}">
                Scope Chain: ${Array.from({length: context.level + 1}, (_, i) => `Level ${i}`).join(' → ')}
            </text>`;
        });

        // Flecha indicando el contexto actual
        svg += `
        <polygon points="350,${startY + 2 * (contextHeight + 10) + 40} 370,${startY + 2 * (contextHeight + 10) + 30} 370,${startY + 2 * (contextHeight + 10) + 50}"
                 fill="${this.colors.primary}"/>
        <text x="380" y="${startY + 2 * (contextHeight + 10) + 45}" class="small-text" fill="${this.colors.primary}">
            Contexto Actual
        </text>`;

        // Código de ejemplo
        const codeExample = this.createCodeBox(400, 100, 350, 180,
            'let globalVar = "global";\n\nfunction outerFunction() {\n  let localVar = "local";\n  \n  function innerFunction() {\n    let blockVar = "block";\n    // Contexto actual: innerFunction\n    console.log(blockVar, localVar, globalVar);\n  }\n  \n  innerFunction(); // ← Aquí\n}'
        );

        // Explicación del call stack
        const explanation = this.createBox(400, 300, 350, 150,
            'Call Stack',
            'Cada llamada a función crea un nuevo contexto de ejecución que se apila. El contexto actual está en la cima del stack. Al retornar, el contexto se elimina.',
            'concept'
        );

        return svg + codeExample + explanation;
    }

    // Métodos para generar ejemplos
    generateClosuresExample() {
        return {
            svg: this.generate('closures'),
            description: 'Diagrama avanzado que explica los closures en JavaScript, incluyendo scope chain, casos de uso prácticos y problemas comunes como memory leaks.'
        };
    }

    generatePrototypesExample() {
        return {
            svg: this.generate('prototypes'),
            description: 'Diagrama que muestra el sistema de prototipos de JavaScript y cómo implementar herencia.'
        };
    }

    generateAsyncAwaitExample() {
        return {
            svg: this.generate('async-await'),
            description: 'Diagrama que explica async/await y el manejo avanzado de errores en operaciones asíncronas.'
        };
    }

    generateModulesExample() {
        return {
            svg: this.generate('modules'),
            description: 'Diagrama que muestra cómo usar módulos ES6 para organizar y estructurar aplicaciones JavaScript.'
        };
    }

    generateDesignPatternsExample() {
        return {
            svg: this.generate('design-patterns'),
            description: 'Diagrama que presenta patrones de diseño comunes en JavaScript como Observer, Factory y Module.'
        };
    }

    generatePerformanceExample() {
        return {
            svg: this.generate('performance'),
            description: 'Diagrama que explica técnicas de optimización de rendimiento en JavaScript.'
        };
    }

    generateTestingExample() {
        return {
            svg: this.generate('testing'),
            description: 'Diagrama que introduce conceptos básicos de testing en JavaScript incluyendo unit tests y mocking.'
        };
    }
}

module.exports = AdvancedGenerator;
