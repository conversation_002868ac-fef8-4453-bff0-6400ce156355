
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">JSON y datos</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
        <g class="box-group">
            <rect x="50" y="80" width="400" height="124" class="concept-box" rx="8" />
            <text x="65" y="105" class="subtitle">Definición</text>
            <text x="65" y="130" class="text">JSON (JavaScript Object Notation) es un</text>
<text x="65" y="148" class="text">formato de intercambio de datos ligero y</text>
<text x="65" y="166" class="text">fácil de leer para humanos y máquinas.</text>

        </g>
        <g class="box-group">
            <rect x="470" y="80" width="400" height="124" class="concept-box" rx="8" />
            <text x="485" y="105" class="subtitle">Sintaxis JSON</text>
            <text x="485" y="130" class="text">Strings entre comillas dobles - Números sin</text>
<text x="485" y="148" class="text">comillas - Booleanos: true/false - null -</text>
<text x="485" y="166" class="text">Arrays: [] - Objetos: {}</text>

        </g>
        <g class="box-group">
            <rect x="50" y="224" width="400" height="124" class="concept-box" rx="8" />
            <text x="65" y="249" class="subtitle">Métodos JSON</text>
            <text x="65" y="274" class="text">JSON.stringify(): Convierte objeto a string</text>
<text x="65" y="292" class="text">JSON - JSON.parse(): Convierte string JSON a</text>
<text x="65" y="310" class="text">objeto JavaScript</text>

        </g>
        <g class="code-group">
            <rect x="470" y="224" width="450" height="232" class="code-box" rx="4" />
            <text x="485" y="249" class="code">// Objeto JavaScript</text>
<text x="485" y="265" class="code">let persona = {</text>
<text x="485" y="281" class="code">  &quot;nombre&quot;: &quot;Ana&quot;,</text>
<text x="485" y="297" class="code">  &quot;edad&quot;: 25,</text>
<text x="485" y="313" class="code">  &quot;activo&quot;: true,</text>
<text x="485" y="329" class="code">  &quot;hobbies&quot;: [&quot;leer&quot;, &quot;programar&quot;]</text>
<text x="485" y="345" class="code">};</text>
<text x="485" y="361" class="code"></text>
<text x="485" y="377" class="code">// Convertir a JSON</text>
<text x="485" y="393" class="code">let json = JSON.stringify(persona);</text>
<text x="485" y="409" class="code">console.log(json); // &#39;{&quot;nombre&quot;:&quot;Ana&quot;,&quot;edad&quot;:25,...}&#39;</text>
<text x="485" y="425" class="code"></text>
<text x="485" y="441" class="code">// Convertir de JSON</text>
<text x="485" y="457" class="code">let objeto = JSON.parse(json);</text>
<text x="485" y="473" class="code">console.log(objeto.nombre); // &quot;Ana&quot;</text>

        </g>
    </g>
</svg>