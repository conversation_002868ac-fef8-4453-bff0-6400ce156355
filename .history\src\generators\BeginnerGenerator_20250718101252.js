const BaseGenerator = require('./BaseGenerator');

class BeginnerGenerator extends BaseGenerator {
    constructor() {
        super();
        
        // Definir temas disponibles para nivel principiante
        this.topics = {
            'variables': 'Variables y tipos de datos',
            'operadores': 'Operadores básicos',
            'condicionales': 'Condicionales (if/else)',
            'bucles': 'Bucles (for, while)',
            'funciones': 'Funciones básicas',
            'arrays': 'Arrays y objetos simples',
            'strings': 'Métodos de string básicos',
            'objetos': 'Objetos y propiedades',
            'eventos': 'Eventos básicos',
            'dom-basico': 'DOM básico',
            'errores': 'Manejo de errores básico',
            'json': 'JSON y datos',
            'fechas': 'Fechas y tiempo',
            'math': 'Matemáticas en JavaScript',
            'regexp': 'Expresiones regulares básicas'
        };
        
        // Ejemplos predefinidos
        this.examples = {
            'variables': this.generateVariablesExample(),
            'operadores': this.generateOperatorsExample(),
            'condicionales': this.generateConditionalsExample(),
            'bucles': this.generateLoopsExample(),
            'funciones': this.generateFunctionsExample(),
            'arrays': this.generateArraysExample(),
            'strings': this.generateStringsExample()
        };
    }

    /**
     * Genera un diagrama SVG para un tema de nivel principiante
     * @param {string} topic - Tema a generar
     * @param {object} options - Opciones adicionales
     * @returns {string} - SVG generado
     */
    generate(topic, options = {}) {
        const normalizedTopic = topic.toLowerCase();
        
        if (!this.hasTopicSupport(normalizedTopic)) {
            throw new Error(`El tema "${topic}" no está soportado para nivel principiante`);
        }
        
        // Obtener el título completo del tema
        const title = this.topics[normalizedTopic];
        
        // Generar el SVG base
        let svg = this.generateBaseSVG(title, options);
        
        // Generar el contenido específico según el tema
        let content = '';
        switch (normalizedTopic) {
            case 'variables':
                content = this.generateVariablesContent();
                break;
            case 'operadores':
                content = this.generateOperatorsContent();
                break;
            case 'condicionales':
                content = this.generateConditionalsContent();
                break;
            case 'bucles':
                content = this.generateLoopsContent();
                break;
            case 'funciones':
                content = this.generateFunctionsContent();
                break;
            case 'arrays':
                content = this.generateArraysContent();
                break;
            case 'strings':
                content = this.generateStringsContent();
                break;
            case 'objetos':
                content = this.generateObjetosContent();
                break;
            case 'eventos':
                content = this.generateEventosContent();
                break;
            case 'dom-basico':
                content = this.generateDOMBasicoContent();
                break;
            case 'errores':
                content = this.generateErroresContent();
                break;
            case 'json':
                content = this.generateJSONContent();
                break;
            case 'fechas':
                content = this.generateFechasContent();
                break;
            case 'math':
                content = this.generateMathContent();
                break;
            case 'regexp':
                content = this.generateRegExpContent();
                break;
            default:
                content = '<text x="400" y="300" text-anchor="middle" class="text">Contenido no disponible</text>';
        }
        
        // Insertar el contenido en el SVG
        svg = svg.replace('<!-- El contenido específico se añadirá aquí -->', content);
        
        return svg;
    }

    /**
     * Genera el contenido para el tema de variables usando layout automático
     * @returns {string} - SVG del contenido
     */
    generateVariablesContent() {
        const elements = [
            {
                type: 'concept',
                title: 'Definición',
                content: 'Las variables son contenedores para almacenar datos. En JavaScript existen tres formas de declarar variables: var, let y const.'
            },
            {
                type: 'concept',
                title: 'Declaración con var',
                content: 'var nombre = valor; Tiene ámbito de función, puede redeclararse y reasignarse. Se eleva (hoisting) al inicio de la función.'
            },
            {
                type: 'concept',
                title: 'Declaración con let',
                content: 'let nombre = valor; Tiene ámbito de bloque, no puede redeclararse pero sí reasignarse. Más seguro que var.'
            },
            {
                type: 'concept',
                title: 'Declaración con const',
                content: 'const nombre = valor; Tiene ámbito de bloque, no puede redeclararse ni reasignarse. Debe inicializarse al declararla.'
            },
            {
                type: 'concept',
                title: 'Tipos de datos primitivos',
                content: 'String: "Hola mundo" - Number: 42, 3.14 - Boolean: true, false - Undefined: undefined - Null: null - Symbol: Symbol("id")'
            },
            {
                type: 'concept',
                title: 'Tipos de datos complejos',
                content: 'Object: {nombre: "Ana", edad: 25} - Array: [1, 2, 3, "texto"] - Function: function() {} - Date: new Date()'
            },
            {
                type: 'code',
                title: 'Ejemplo práctico',
                content: 'let nombre = "Ana";\nconst edad = 25;\nlet esEstudiante = true;\nlet hobbies = ["leer", "programar"];\n\nconsole.log(nombre); // "Ana"\nconsole.log(typeof edad); // "number"\nconsole.log(esEstudiante); // true'
            },
            {
                type: 'warning',
                title: 'Errores comunes',
                content: 'Usar const para valores que necesitan cambiar. Usar var en lugar de let. No inicializar variables. Confundir null con undefined.'
            },
            {
                type: 'example',
                title: 'Buenas prácticas',
                content: 'Usar const por defecto. Usar let cuando necesites reasignar. Evitar var. Usar nombres descriptivos. Inicializar variables al declararlas.'
            }
        ];

        const layout = this.createAutoLayout(elements, {
            startX: 50,
            startY: 80,
            maxWidth: 1100,
            columnsPreferred: 2
        });

        let content = '';
        layout.elements.forEach(element => {
            if (element.type === 'code') {
                content += this.createCodeBox(element.x, element.y, element.width, element.height, element.content);
            } else {
                content += this.createBox(element.x, element.y, element.width, element.height, element.title, element.content, element.type);
            }
        });

        return content;
    }

    /**
     * Genera el contenido para el tema de operadores
     * @returns {string} - SVG del contenido
     */
    generateOperatorsContent() {
        return `
        <!-- Definición -->
        ${this.createBox(50, 80, 700, 80, 'Definición', 'Los operadores permiten realizar operaciones en variables y valores. JavaScript incluye operadores aritméticos, de asignación, de comparación y lógicos.', 'concept')}
        
        <!-- Operadores aritméticos -->
        ${this.createBox(50, 180, 320, 150, 'Operadores aritméticos', '+  Suma\n-  Resta\n*  Multiplicación\n/  División\n%  Módulo (resto)\n** Exponenciación', 'concept')}
        
        <!-- Operadores de comparación -->
        ${this.createBox(430, 180, 320, 150, 'Operadores de comparación', '==  Igual (valor)\n=== Igual (valor y tipo)\n!=  Diferente (valor)\n!== Diferente (valor y tipo)\n>   Mayor que\n<   Menor que', 'concept')}
        
        <!-- Operadores lógicos -->
        ${this.createBox(50, 350, 320, 100, 'Operadores lógicos', '&&  AND lógico\n||  OR lógico\n!   NOT lógico', 'concept')}
        
        <!-- Operadores de asignación -->
        ${this.createBox(430, 350, 320, 100, 'Operadores de asignación', '=   Asignación\n+=  Suma y asignación\n-=  Resta y asignación', 'concept')}
        
        <!-- Ejemplo práctico -->
        ${this.createCodeBox(50, 470, 700, 120, 'let a = 5;\nlet b = 2;\n\nlet suma = a + b;     // 7\nlet comparacion = a > b;  // true\nlet logico = (a > 0) && (b < 5);  // true')}
        `;
    }

    /**
     * Genera el contenido para el tema de condicionales
     * @returns {string} - SVG del contenido
     */
    generateConditionalsContent() {
        return `
        <!-- Definición -->
        ${this.createBox(50, 80, 700, 80, 'Definición', 'Las estructuras condicionales permiten ejecutar diferentes bloques de código dependiendo de si una condición es verdadera o falsa.', 'concept')}
        
        <!-- if/else -->
        ${this.createBox(50, 180, 320, 150, 'if/else', 'if (condición) {\n  // código si es verdadero\n} else {\n  // código si es falso\n}', 'concept')}
        
        <!-- if/else if/else -->
        ${this.createBox(430, 180, 320, 150, 'if/else if/else', 'if (condición1) {\n  // código si condición1 es verdadera\n} else if (condición2) {\n  // código si condición2 es verdadera\n} else {\n  // código si ninguna es verdadera\n}', 'concept')}
        
        <!-- Operador ternario -->
        ${this.createBox(50, 350, 320, 100, 'Operador ternario', 'condición ? valorSiVerdadero : valorSiFalso', 'concept')}
        
        <!-- Switch -->
        ${this.createBox(430, 350, 320, 150, 'Switch', 'switch(expresión) {\n  case valor1:\n    // código\n    break;\n  case valor2:\n    // código\n    break;\n  default:\n    // código por defecto\n}', 'concept')}
        
        <!-- Ejemplo práctico -->
        ${this.createCodeBox(50, 520, 700, 120, 'let edad = 18;\n\nif (edad >= 18) {\n  console.log("Eres mayor de edad");\n} else {\n  console.log("Eres menor de edad");\n}\n\n// Usando operador ternario\nlet mensaje = edad >= 18 ? "Mayor de edad" : "Menor de edad";')}
        `;
    }

    /**
     * Genera el contenido para el tema de bucles
     * @returns {string} - SVG del contenido
     */
    generateLoopsContent() {
        return `
        <!-- Definición -->
        ${this.createBox(50, 80, 700, 80, 'Definición', 'Los bucles permiten ejecutar un bloque de código repetidamente mientras se cumpla una condición específica.', 'concept')}

        <!-- For loop -->
        ${this.createBox(50, 180, 320, 150, 'Bucle for', 'for (inicialización; condición; incremento) {\n  // código a repetir\n}\n\nEjemplo:\nfor (let i = 0; i < 5; i++) {\n  console.log(i);\n}', 'concept')}

        <!-- While loop -->
        ${this.createBox(430, 180, 320, 150, 'Bucle while', 'while (condición) {\n  // código a repetir\n}\n\nEjemplo:\nlet i = 0;\nwhile (i < 5) {\n  console.log(i);\n  i++;\n}', 'concept')}

        <!-- Do-while loop -->
        ${this.createBox(50, 350, 320, 120, 'Bucle do-while', 'do {\n  // código a repetir\n} while (condición);\n\nSe ejecuta al menos una vez', 'concept')}

        <!-- Break y Continue -->
        ${this.createBox(430, 350, 320, 120, 'Break y Continue', 'break: Sale del bucle\ncontinue: Salta a la siguiente iteración', 'concept')}

        <!-- Ejemplo práctico -->
        ${this.createCodeBox(50, 490, 700, 100, 'for (let i = 1; i <= 10; i++) {\n  if (i % 2 === 0) {\n    console.log(i + " es par");\n  }\n}')}
        `;
    }

    /**
     * Genera el contenido para el tema de funciones
     * @returns {string} - SVG del contenido
     */
    generateFunctionsContent() {
        return `
        <!-- Definición -->
        ${this.createBox(50, 80, 700, 80, 'Definición', 'Las funciones son bloques de código reutilizables que realizan una tarea específica. Pueden recibir parámetros y devolver valores.', 'concept')}

        <!-- Declaración de función -->
        ${this.createBox(50, 180, 320, 150, 'Declaración de función', 'function nombreFuncion(parametros) {\n  // código de la función\n  return valor; // opcional\n}\n\nEjemplo:\nfunction saludar(nombre) {\n  return "Hola " + nombre;\n}', 'concept')}

        <!-- Expresión de función -->
        ${this.createBox(430, 180, 320, 150, 'Expresión de función', 'const nombreFuncion = function(parametros) {\n  // código de la función\n};\n\nEjemplo:\nconst sumar = function(a, b) {\n  return a + b;\n};', 'concept')}

        <!-- Llamada a función -->
        ${this.createBox(50, 350, 320, 100, 'Llamada a función', 'nombreFuncion(argumentos);\n\nEjemplo:\nlet resultado = sumar(5, 3);', 'concept')}

        <!-- Ámbito de variables -->
        ${this.createBox(430, 350, 320, 100, 'Ámbito (Scope)', 'Variables locales: Solo dentro de la función\nVariables globales: Accesibles desde cualquier lugar', 'concept')}

        <!-- Ejemplo práctico -->
        ${this.createCodeBox(50, 470, 700, 120, 'function calcularArea(radio) {\n  const pi = 3.14159;\n  return pi * radio * radio;\n}\n\nlet area = calcularArea(5);\nconsole.log("El área es: " + area);')}
        `;
    }

    /**
     * Genera el contenido para el tema de arrays
     * @returns {string} - SVG del contenido
     */
    generateArraysContent() {
        return `
        <!-- Definición -->
        ${this.createBox(50, 80, 700, 80, 'Definición', 'Los arrays son estructuras de datos que almacenan múltiples valores en una sola variable. Los objetos almacenan pares clave-valor.', 'concept')}

        <!-- Arrays -->
        ${this.createBox(50, 180, 320, 150, 'Arrays', 'let array = [elemento1, elemento2, ...];\n\nAcceso: array[índice]\nLongitud: array.length\n\nEjemplo:\nlet frutas = ["manzana", "banana"];\nconsole.log(frutas[0]); // "manzana"', 'concept')}

        <!-- Objetos -->
        ${this.createBox(430, 180, 320, 150, 'Objetos', 'let objeto = {\n  clave1: valor1,\n  clave2: valor2\n};\n\nAcceso: objeto.clave o objeto["clave"]\n\nEjemplo:\nlet persona = {nombre: "Ana", edad: 25};', 'concept')}

        <!-- Métodos de array -->
        ${this.createBox(50, 350, 320, 120, 'Métodos básicos de array', 'push(): Añade al final\npop(): Elimina del final\nshift(): Elimina del inicio\nunshift(): Añade al inicio\nlength: Longitud del array', 'concept')}

        <!-- Propiedades de objeto -->
        ${this.createBox(430, 350, 320, 120, 'Trabajar con objetos', 'Añadir: objeto.nuevaClave = valor\nEliminar: delete objeto.clave\nVerificar: "clave" in objeto', 'concept')}

        <!-- Ejemplo práctico -->
        ${this.createCodeBox(50, 490, 700, 100, 'let estudiantes = ["Ana", "Luis", "María"];\nestudiantes.push("Carlos");\n\nlet estudiante = {nombre: "Ana", edad: 20, curso: "JavaScript"};\nconsole.log(estudiante.nombre + " tiene " + estudiante.edad + " años");')}
        `;
    }

    /**
     * Genera el contenido para el tema de strings
     * @returns {string} - SVG del contenido
     */
    generateStringsContent() {
        const elements = [
            {
                type: 'concept',
                title: 'Definición',
                content: 'Los strings (cadenas de texto) son secuencias de caracteres. JavaScript proporciona muchos métodos para manipular strings.'
            },
            {
                type: 'concept',
                title: 'Creación de strings',
                content: 'Comillas dobles: "Hola mundo" - Comillas simples: \'Hola mundo\' - Template literals: `Hola ${nombre}` - Concatenación: "Hola" + " " + "mundo"'
            },
            {
                type: 'concept',
                title: 'Métodos básicos',
                content: 'length: Longitud del string - toUpperCase(): Convertir a mayúsculas - toLowerCase(): Convertir a minúsculas - indexOf(): Buscar posición - slice(): Extraer parte'
            },
            {
                type: 'concept',
                title: 'Métodos avanzados',
                content: 'replace(): Reemplazar texto - split(): Dividir en array - trim(): Eliminar espacios - includes(): Verificar si contiene - startsWith(): Verificar inicio'
            },
            {
                type: 'concept',
                title: 'Template literals',
                content: 'Permiten insertar variables y expresiones usando ${} dentro de backticks. Soportan múltiples líneas sin caracteres de escape.'
            },
            {
                type: 'concept',
                title: 'Caracteres especiales',
                content: '\\n: Nueva línea - \\t: Tabulación - \\": Comilla doble - \\\': Comilla simple - \\\\: Barra invertida - \\r: Retorno de carro'
            },
            {
                type: 'code',
                title: 'Ejemplo práctico',
                content: 'let texto = "JavaScript es genial";\nconsole.log(texto.length);        // 20\nconsole.log(texto.toUpperCase()); // "JAVASCRIPT ES GENIAL"\nconsole.log(texto.indexOf("Script")); // 4\nconsole.log(texto.slice(0, 10));  // "JavaScript"\n\nlet nombre = "Ana";\nlet saludo = `Hola ${nombre}!`; // "Hola Ana!"'
            }
        ];

        const layout = this.createAutoLayout(elements);
        let content = '';
        layout.elements.forEach(element => {
            if (element.type === 'code') {
                content += this.createCodeBox(element.x, element.y, element.width, element.height, element.content);
            } else {
                content += this.createBox(element.x, element.y, element.width, element.height, element.title, element.content, element.type);
            }
        });

        return content;
    }

    /**
     * Genera el contenido para el tema de objetos
     * @returns {string} - SVG del contenido
     */
    generateObjetosContent() {
        const elements = [
            {
                type: 'concept',
                title: 'Definición',
                content: 'Los objetos son colecciones de propiedades (pares clave-valor). Son la base de JavaScript y permiten agrupar datos relacionados.'
            },
            {
                type: 'concept',
                title: 'Creación de objetos',
                content: 'Literal: {nombre: "Ana", edad: 25} - Constructor: new Object() - Object.create() - Clases: new MiClase()'
            },
            {
                type: 'concept',
                title: 'Acceso a propiedades',
                content: 'Notación punto: objeto.propiedad - Notación corchetes: objeto["propiedad"] - Variables: objeto[variable]'
            },
            {
                type: 'concept',
                title: 'Modificar propiedades',
                content: 'Añadir: objeto.nuevaPropiedad = valor - Modificar: objeto.propiedad = nuevoValor - Eliminar: delete objeto.propiedad'
            },
            {
                type: 'concept',
                title: 'Métodos de objeto',
                content: 'Object.keys(): Obtener claves - Object.values(): Obtener valores - Object.entries(): Obtener pares clave-valor - hasOwnProperty(): Verificar propiedad'
            },
            {
                type: 'code',
                title: 'Ejemplo práctico',
                content: 'let persona = {\n  nombre: "Ana",\n  edad: 25,\n  saludar: function() {\n    return "Hola, soy " + this.nombre;\n  }\n};\n\nconsole.log(persona.nombre); // "Ana"\nconsole.log(persona.saludar()); // "Hola, soy Ana"\npersona.apellido = "García"; // Añadir propiedad'
            }
        ];

        const layout = this.createAutoLayout(elements);
        let content = '';
        layout.elements.forEach(element => {
            if (element.type === 'code') {
                content += this.createCodeBox(element.x, element.y, element.width, element.height, element.content);
            } else {
                content += this.createBox(element.x, element.y, element.width, element.height, element.title, element.content, element.type);
            }
        });

        return content;
    }

    /**
     * Genera el contenido para el tema de eventos básicos
     * @returns {string} - SVG del contenido
     */
    generateEventosContent() {
        const elements = [
            {
                type: 'concept',
                title: 'Definición',
                content: 'Los eventos son acciones que ocurren en la página web, como clicks, teclas presionadas, carga de página, etc.'
            },
            {
                type: 'concept',
                title: 'Tipos de eventos comunes',
                content: 'click: Click del mouse - keydown/keyup: Teclas - load: Carga de página - submit: Envío de formulario - change: Cambio en input'
            },
            {
                type: 'concept',
                title: 'addEventListener',
                content: 'Método para escuchar eventos: elemento.addEventListener("evento", función). Es la forma moderna y recomendada.'
            },
            {
                type: 'concept',
                title: 'Event object',
                content: 'Objeto que contiene información del evento: event.target (elemento), event.type (tipo), event.preventDefault() (cancelar)'
            },
            {
                type: 'code',
                title: 'Ejemplo práctico',
                content: 'let boton = document.getElementById("miBoton");\n\nboton.addEventListener("click", function(event) {\n  console.log("¡Botón clickeado!");\n  console.log("Elemento:", event.target);\n});\n\n// Evento de teclado\ndocument.addEventListener("keydown", function(event) {\n  console.log("Tecla presionada:", event.key);\n});'
            }
        ];

        const layout = this.createAutoLayout(elements);
        let content = '';
        layout.elements.forEach(element => {
            if (element.type === 'code') {
                content += this.createCodeBox(element.x, element.y, element.width, element.height, element.content);
            } else {
                content += this.createBox(element.x, element.y, element.width, element.height, element.title, element.content, element.type);
            }
        });

        return content;
    }

    /**
     * Genera el contenido para el tema de DOM básico
     * @returns {string} - SVG del contenido
     */
    generateDOMBasicoContent() {
        const elements = [
            {
                type: 'concept',
                title: 'Definición',
                content: 'El DOM (Document Object Model) es la representación en JavaScript de la estructura HTML de una página web.'
            },
            {
                type: 'concept',
                title: 'Seleccionar elementos',
                content: 'getElementById(): Por ID - getElementsByClassName(): Por clase - getElementsByTagName(): Por etiqueta - querySelector(): Primer elemento que coincida'
            },
            {
                type: 'concept',
                title: 'Modificar contenido',
                content: 'innerHTML: Cambiar HTML interno - textContent: Cambiar solo texto - value: Valor de inputs - setAttribute(): Cambiar atributos'
            },
            {
                type: 'concept',
                title: 'Modificar estilos',
                content: 'style.propiedad: Cambiar CSS - className: Cambiar clase - classList.add/remove(): Gestionar clases'
            },
            {
                type: 'code',
                title: 'Ejemplo práctico',
                content: '// Seleccionar elemento\nlet titulo = document.getElementById("titulo");\n\n// Cambiar contenido\ntitulo.textContent = "Nuevo título";\n\n// Cambiar estilo\ntitulo.style.color = "blue";\n\n// Añadir clase\ntitulo.classList.add("destacado");\n\n// Crear nuevo elemento\nlet parrafo = document.createElement("p");\nparrafo.textContent = "Nuevo párrafo";\ndocument.body.appendChild(parrafo);'
            }
        ];

        const layout = this.createAutoLayout(elements);
        let content = '';
        layout.elements.forEach(element => {
            if (element.type === 'code') {
                content += this.createCodeBox(element.x, element.y, element.width, element.height, element.content);
            } else {
                content += this.createBox(element.x, element.y, element.width, element.height, element.title, element.content, element.type);
            }
        });

        return content;
    }

    /**
     * Genera el contenido para el tema de manejo de errores básico
     * @returns {string} - SVG del contenido
     */
    generateErroresContent() {
        const elements = [
            {
                type: 'concept',
                title: 'Definición',
                content: 'Los errores son problemas que ocurren durante la ejecución del código. JavaScript proporciona mecanismos para manejarlos.'
            },
            {
                type: 'concept',
                title: 'Tipos de errores',
                content: 'SyntaxError: Error de sintaxis - ReferenceError: Variable no definida - TypeError: Tipo incorrecto - RangeError: Valor fuera de rango'
            },
            {
                type: 'concept',
                title: 'try...catch',
                content: 'try: Bloque de código a probar - catch: Maneja el error si ocurre - finally: Se ejecuta siempre - throw: Lanza un error personalizado'
            },
            {
                type: 'code',
                title: 'Ejemplo práctico',
                content: 'try {\n  let resultado = 10 / 0;\n  console.log(resultado);\n  \n  // Código que puede fallar\n  let objeto = null;\n  console.log(objeto.propiedad); // Error\n} catch (error) {\n  console.log("Error:", error.message);\n} finally {\n  console.log("Esto siempre se ejecuta");\n}'
            }
        ];

        const layout = this.createAutoLayout(elements);
        let content = '';
        layout.elements.forEach(element => {
            if (element.type === 'code') {
                content += this.createCodeBox(element.x, element.y, element.width, element.height, element.content);
            } else {
                content += this.createBox(element.x, element.y, element.width, element.height, element.title, element.content, element.type);
            }
        });

        return content;
    }

    /**
     * Genera el contenido para el tema de JSON
     * @returns {string} - SVG del contenido
     */
    generateJSONContent() {
        const elements = [
            {
                type: 'concept',
                title: 'Definición',
                content: 'JSON (JavaScript Object Notation) es un formato de intercambio de datos ligero y fácil de leer para humanos y máquinas.'
            },
            {
                type: 'concept',
                title: 'Sintaxis JSON',
                content: 'Strings entre comillas dobles - Números sin comillas - Booleanos: true/false - null - Arrays: [] - Objetos: {}'
            },
            {
                type: 'concept',
                title: 'Métodos JSON',
                content: 'JSON.stringify(): Convierte objeto a string JSON - JSON.parse(): Convierte string JSON a objeto JavaScript'
            },
            {
                type: 'code',
                title: 'Ejemplo práctico',
                content: '// Objeto JavaScript\nlet persona = {\n  "nombre": "Ana",\n  "edad": 25,\n  "activo": true,\n  "hobbies": ["leer", "programar"]\n};\n\n// Convertir a JSON\nlet json = JSON.stringify(persona);\nconsole.log(json); // \'{"nombre":"Ana","edad":25,...}\'\n\n// Convertir de JSON\nlet objeto = JSON.parse(json);\nconsole.log(objeto.nombre); // "Ana"'
            }
        ];

        const layout = this.createAutoLayout(elements);
        let content = '';
        layout.elements.forEach(element => {
            if (element.type === 'code') {
                content += this.createCodeBox(element.x, element.y, element.width, element.height, element.content);
            } else {
                content += this.createBox(element.x, element.y, element.width, element.height, element.title, element.content, element.type);
            }
        });

        return content;
    }

    /**
     * Genera el contenido para el tema de fechas
     * @returns {string} - SVG del contenido
     */
    generateFechasContent() {
        const elements = [
            {
                type: 'concept',
                title: 'Definición',
                content: 'El objeto Date en JavaScript permite trabajar con fechas y horas. Representa un momento específico en el tiempo.'
            },
            {
                type: 'concept',
                title: 'Crear fechas',
                content: 'new Date(): Fecha actual - new Date(año, mes, día) - new Date("2023-12-25") - Date.now(): Timestamp actual'
            },
            {
                type: 'concept',
                title: 'Métodos principales',
                content: 'getFullYear(): Año - getMonth(): Mes (0-11) - getDate(): Día - getHours(): Hora - getMinutes(): Minutos - getTime(): Timestamp'
            },
            {
                type: 'code',
                title: 'Ejemplo práctico',
                content: '// Crear fechas\nlet ahora = new Date();\nlet navidad = new Date(2023, 11, 25); // Mes 11 = Diciembre\n\n// Obtener información\nconsole.log(ahora.getFullYear()); // 2023\nconsole.log(ahora.getMonth() + 1);  // Mes actual (sumar 1)\nconsole.log(ahora.getDate());     // Día del mes\n\n// Formatear fecha\nconsole.log(ahora.toLocaleDateString()); // "25/12/2023"'
            }
        ];

        const layout = this.createAutoLayout(elements);
        let content = '';
        layout.elements.forEach(element => {
            if (element.type === 'code') {
                content += this.createCodeBox(element.x, element.y, element.width, element.height, element.content);
            } else {
                content += this.createBox(element.x, element.y, element.width, element.height, element.title, element.content, element.type);
            }
        });

        return content;
    }

    /**
     * Genera el contenido para el tema de Math
     * @returns {string} - SVG del contenido
     */
    generateMathContent() {
        const elements = [
            {
                type: 'concept',
                title: 'Definición',
                content: 'El objeto Math proporciona propiedades y métodos para constantes y funciones matemáticas. No es un constructor.'
            },
            {
                type: 'concept',
                title: 'Constantes Math',
                content: 'Math.PI: π (3.14159...) - Math.E: e (2.718...) - Math.LN2: ln(2) - Math.SQRT2: √2'
            },
            {
                type: 'concept',
                title: 'Métodos básicos',
                content: 'Math.abs(): Valor absoluto - Math.round(): Redondear - Math.floor(): Redondear hacia abajo - Math.ceil(): Redondear hacia arriba'
            },
            {
                type: 'concept',
                title: 'Métodos avanzados',
                content: 'Math.random(): Número aleatorio (0-1) - Math.max(): Máximo - Math.min(): Mínimo - Math.pow(): Potencia - Math.sqrt(): Raíz cuadrada'
            },
            {
                type: 'code',
                title: 'Ejemplo práctico',
                content: '// Operaciones básicas\nconsole.log(Math.abs(-5));    // 5\nconsole.log(Math.round(4.7)); // 5\nconsole.log(Math.floor(4.7)); // 4\nconsole.log(Math.ceil(4.1));  // 5\n\n// Números aleatorios\nlet aleatorio = Math.random(); // 0.0 - 0.999...\nlet dado = Math.floor(Math.random() * 6) + 1; // 1-6\n\n// Máximo y mínimo\nconsole.log(Math.max(1, 5, 3)); // 5'
            }
        ];

        const layout = this.createAutoLayout(elements);
        let content = '';
        layout.elements.forEach(element => {
            if (element.type === 'code') {
                content += this.createCodeBox(element.x, element.y, element.width, element.height, element.content);
            } else {
                content += this.createBox(element.x, element.y, element.width, element.height, element.title, element.content, element.type);
            }
        });

        return content;
    }

    /**
     * Genera el contenido para el tema de expresiones regulares básicas
     * @returns {string} - SVG del contenido
     */
    generateRegExpContent() {
        const elements = [
            {
                type: 'concept',
                title: 'Definición',
                content: 'Las expresiones regulares (RegExp) son patrones para buscar y manipular texto. Son muy útiles para validación y búsqueda.'
            },
            {
                type: 'concept',
                title: 'Crear RegExp',
                content: 'Literal: /patrón/flags - Constructor: new RegExp("patrón", "flags") - Flags: g (global), i (insensible), m (multilínea)'
            },
            {
                type: 'concept',
                title: 'Métodos básicos',
                content: 'test(): Verifica si coincide - exec(): Busca coincidencia - match(): Busca en string - replace(): Reemplaza coincidencias'
            },
            {
                type: 'concept',
                title: 'Patrones básicos',
                content: '. : Cualquier carácter - * : Cero o más - + : Uno o más - ? : Cero o uno - [abc] : Cualquiera de a, b, c - \\d : Dígito'
            },
            {
                type: 'code',
                title: 'Ejemplo práctico',
                content: '// Crear expresión regular\nlet patron = /\\d+/g; // Buscar números\nlet email = /^[\\w.-]+@[\\w.-]+\\.[a-zA-Z]{2,}$/;\n\n// Usar test()\nconsole.log(patron.test("123")); // true\nconsole.log(email.test("<EMAIL>")); // true\n\n// Usar con strings\nlet texto = "Tengo 25 años";\nconsole.log(texto.match(/\\d+/)); // ["25"]\nconsole.log(texto.replace(/\\d+/, "XX")); // "Tengo XX años"'
            }
        ];

        const layout = this.createAutoLayout(elements);
        let content = '';
        layout.elements.forEach(element => {
            if (element.type === 'code') {
                content += this.createCodeBox(element.x, element.y, element.width, element.height, element.content);
            } else {
                content += this.createBox(element.x, element.y, element.width, element.height, element.title, element.content, element.type);
            }
        });

        return content;
    }

    // Métodos para generar ejemplos
    generateVariablesExample() {
        return {
            svg: this.generate('variables'),
            description: 'Diagrama que muestra los conceptos básicos de variables en JavaScript, incluyendo declaración, tipos de datos y ejemplos de uso.'
        };
    }

    generateOperatorsExample() {
        return {
            svg: this.generate('operadores'),
            description: 'Diagrama que muestra los diferentes tipos de operadores en JavaScript: aritméticos, de comparación, lógicos y de asignación.'
        };
    }

    generateConditionalsExample() {
        return {
            svg: this.generate('condicionales'),
            description: 'Diagrama que explica las estructuras condicionales en JavaScript, incluyendo if/else, operador ternario y switch.'
        };
    }

    generateLoopsExample() {
        return {
            svg: this.generate('bucles'),
            description: 'Diagrama que muestra los diferentes tipos de bucles en JavaScript: for, while y do-while.'
        };
    }

    generateFunctionsExample() {
        return {
            svg: this.generate('funciones'),
            description: 'Diagrama que explica los conceptos básicos de funciones en JavaScript, incluyendo declaración, parámetros y retorno.'
        };
    }

    generateArraysExample() {
        return {
            svg: this.generate('arrays'),
            description: 'Diagrama que muestra los conceptos básicos de arrays y objetos en JavaScript, incluyendo creación, acceso y métodos comunes.'
        };
    }

    generateStringsExample() {
        return {
            svg: this.generate('strings'),
            description: 'Diagrama que explica los métodos básicos para manipular strings en JavaScript.'
        };
    }
}

module.exports = BeginnerGenerator;
