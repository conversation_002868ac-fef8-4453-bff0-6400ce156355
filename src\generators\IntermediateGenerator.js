const BaseGenerator = require('./BaseGenerator');

class IntermediateGenerator extends BaseGenerator {
    constructor() {
        super();
        
        // Definir temas disponibles para nivel intermedio
        this.topics = {
            'scope': 'Scope y hoisting',
            'arrow-functions': 'Arrow functions',
            'destructuring': 'Destructuring',
            'spread-operator': 'Spread operator',
            'array-methods': 'Métodos de array (map, filter, reduce)',
            'promises': 'Promesas básicas',
            'dom': 'Manipulación del DOM',
            'events': 'Event handling',
            'fetch-api': 'Fetch API y AJAX',
            'local-storage': 'LocalStorage y SessionStorage',
            'template-literals': 'Template literals avanzados',
            'classes': 'Clases ES6',
            'modules-basic': 'Módulos básicos',
            'async-basics': 'Programación asíncrona básica',
            'error-handling': 'Manejo avanzado de errores',
            'form-validation': 'Validación de formularios',
            'timers': 'Timers (setTimeout, setInterval)',
            'control-flow': 'Diagramas de flujo de control',
            'execution-flow': 'Flujo de ejecución de funciones',
            'conditional-flow': 'Flujo condicional y bucles'
        };
        
        // Ejemplos predefinidos
        this.examples = {
            'scope': this.generateScopeExample(),
            'arrow-functions': this.generateArrowFunctionsExample(),
            'destructuring': this.generateDestructuringExample(),
            'spread-operator': this.generateSpreadOperatorExample(),
            'array-methods': this.generateArrayMethodsExample(),
            'promises': this.generatePromisesExample(),
            'dom': this.generateDOMExample(),
            'events': this.generateEventsExample()
        };
    }

    /**
     * Genera un diagrama SVG para un tema de nivel intermedio
     * @param {string} topic - Tema a generar
     * @param {object} options - Opciones adicionales
     * @returns {string} - SVG generado
     */
    generate(topic, options = {}) {
        const normalizedTopic = topic.toLowerCase();
        
        if (!this.hasTopicSupport(normalizedTopic)) {
            throw new Error(`El tema "${topic}" no está soportado para nivel intermedio`);
        }
        
        // Obtener el título completo del tema
        const title = this.topics[normalizedTopic];
        
        // Generar el SVG base con dimensiones más grandes para contenido complejo
        const svgOptions = { width: 1000, height: 700, ...options };
        let svg = this.generateBaseSVG(title, svgOptions);
        
        // Generar el contenido específico según el tema
        let content = '';
        switch (normalizedTopic) {
            case 'scope':
                content = this.generateScopeContent();
                break;
            case 'arrow-functions':
                content = this.generateArrowFunctionsContent();
                break;
            case 'destructuring':
                content = this.generateDestructuringContent();
                break;
            case 'spread-operator':
                content = this.generateSpreadOperatorContent();
                break;
            case 'array-methods':
                content = this.generateArrayMethodsContent();
                break;
            case 'promises':
                content = this.generatePromisesContent();
                break;
            case 'dom':
                content = this.generateDOMContent();
                break;
            case 'events':
                content = this.generateEventsContent();
                break;
            case 'control-flow':
                content = this.generateControlFlowContent();
                break;
            case 'execution-flow':
                content = this.generateExecutionFlowContent();
                break;
            case 'conditional-flow':
                content = this.generateConditionalFlowContent();
                break;
            default:
                content = '<text x="500" y="350" text-anchor="middle" class="text">Contenido no disponible</text>';
        }
        
        // Insertar el contenido en el SVG
        svg = svg.replace('<!-- El contenido específico se añadirá aquí -->', content);
        
        return svg;
    }

    /**
     * Genera el contenido para el tema de scope y hoisting
     * @returns {string} - SVG del contenido
     */
    generateScopeContent() {
        return `
        <!-- Prerequisitos -->
        ${this.createBox(50, 80, 900, 60, 'Prerequisitos', 'Variables (var, let, const), Funciones básicas', 'warning')}
        
        <!-- Definición de Scope -->
        ${this.createBox(50, 160, 430, 120, 'Scope (Ámbito)', 'El scope determina dónde las variables son accesibles en el código.\n\nTipos:\n• Global scope: Accesible en todo el programa\n• Function scope: Solo dentro de la función\n• Block scope: Solo dentro del bloque {}', 'concept')}
        
        <!-- Hoisting -->
        ${this.createBox(520, 160, 430, 120, 'Hoisting', 'JavaScript "eleva" las declaraciones al inicio de su scope.\n\n• var: Se eleva, se inicializa con undefined\n• let/const: Se elevan, pero no se inicializan\n• function: Se eleva completamente', 'concept')}
        
        <!-- Ejemplo de Scope -->
        ${this.createCodeBox(50, 300, 430, 150, 'var globalVar = "Global";\n\nfunction ejemplo() {\n  var funcionVar = "Función";\n  \n  if (true) {\n    let bloqueVar = "Bloque";\n    const bloqueConst = "Constante";\n  }\n  \n  // bloqueVar no es accesible aquí\n}')}
        
        <!-- Ejemplo de Hoisting -->
        ${this.createCodeBox(520, 300, 430, 150, '// Lo que escribes:\nconsole.log(x); // undefined\nvar x = 5;\n\n// Lo que JavaScript interpreta:\nvar x;\nconsole.log(x); // undefined\nx = 5;\n\n// Con let/const da error:\nconsole.log(y); // ReferenceError\nlet y = 10;')}
        
        <!-- Buenas prácticas -->
        ${this.createBox(50, 470, 900, 80, 'Buenas prácticas', 'Usar let y const en lugar de var. Declarar variables al inicio de su scope. Evitar variables globales innecesarias. Usar const por defecto, let cuando necesites reasignar.', 'example')}
        
        <!-- Flecha explicativa -->
        ${this.createArrow(280, 280, 280, 300, 'Scope')}
        ${this.createArrow(720, 280, 720, 300, 'Hoisting')}
        `;
    }

    /**
     * Genera el contenido para el tema de arrow functions
     * @returns {string} - SVG del contenido
     */
    generateArrowFunctionsContent() {
        return `
        <!-- Prerequisitos -->
        ${this.createBox(50, 80, 900, 60, 'Prerequisitos', 'Funciones básicas, this keyword, métodos de array', 'warning')}
        
        <!-- Definición -->
        ${this.createBox(50, 160, 900, 80, 'Definición', 'Las arrow functions son una forma más concisa de escribir funciones. Tienen un comportamiento especial con el contexto (this) y no pueden ser usadas como constructores.', 'concept')}
        
        <!-- Sintaxis básica -->
        ${this.createBox(50, 260, 430, 150, 'Sintaxis básica', 'Función tradicional:\nfunction(param) { return param * 2; }\n\nArrow function:\n(param) => param * 2\n\nCon múltiples parámetros:\n(a, b) => a + b\n\nSin parámetros:\n() => "Hola mundo"', 'concept')}
        
        <!-- Sintaxis con bloque -->
        ${this.createBox(520, 260, 430, 150, 'Con bloque de código', 'Arrow function con {}:\n(param) => {\n  const resultado = param * 2;\n  return resultado;\n}\n\nSin {}, return implícito:\n(param) => param * 2', 'concept')}
        
        <!-- Diferencias con this -->
        ${this.createCodeBox(50, 430, 430, 120, '// Función tradicional\nconst obj = {\n  nombre: "Ana",\n  saludar: function() {\n    console.log("Hola " + this.nombre);\n  }\n};\nobj.saludar(); // "Hola Ana"')}
        
        <!-- Arrow function y this -->
        ${this.createCodeBox(520, 430, 430, 120, '// Arrow function\nconst obj = {\n  nombre: "Ana",\n  saludar: () => {\n    console.log("Hola " + this.nombre);\n  }\n};\nobj.saludar(); // "Hola undefined"')}
        
        <!-- Casos de uso -->
        ${this.createBox(50, 570, 900, 80, 'Casos de uso comunes', 'Callbacks en métodos de array (map, filter, reduce). Event listeners. Funciones cortas y simples. Cuando no necesitas tu propio contexto this.', 'example')}
        `;
    }

    // Métodos para generar contenido de otros temas (implementación básica)
    generateDestructuringContent() {
        return `<!-- Contenido para destructuring -->`;
    }

    generateSpreadOperatorContent() {
        return `<!-- Contenido para spread operator -->`;
    }

    generateArrayMethodsContent() {
        return `<!-- Contenido para métodos de array -->`;
    }

    generatePromisesContent() {
        return `<!-- Contenido para promesas -->`;
    }

    generateDOMContent() {
        return `<!-- Contenido para DOM -->`;
    }

    generateEventsContent() {
        return `<!-- Contenido para eventos -->`;
    }

    /**
     * Genera diagrama de flujo de control
     * @returns {string} - SVG del contenido
     */
    generateControlFlowContent() {
        // Definir nodos del diagrama de flujo
        const nodes = [
            { id: 'start', type: 'start', label: 'Inicio', x: 350, y: 50 },
            { id: 'input', type: 'process', label: 'Leer entrada', x: 350, y: 150 },
            { id: 'validate', type: 'decision', label: '¿Es válida?', x: 350, y: 250 },
            { id: 'process', type: 'process', label: 'Procesar datos', x: 200, y: 350 },
            { id: 'error', type: 'process', label: 'Mostrar error', x: 500, y: 350 },
            { id: 'output', type: 'process', label: 'Mostrar resultado', x: 200, y: 450 },
            { id: 'end', type: 'end', label: 'Fin', x: 350, y: 550 }
        ];

        // Definir conexiones
        const connections = [
            { from: 'start', to: 'input' },
            { from: 'input', to: 'validate' },
            { from: 'validate', to: 'process', label: 'Sí', type: 'true' },
            { from: 'validate', to: 'error', label: 'No', type: 'false' },
            { from: 'process', to: 'output' },
            { from: 'output', to: 'end' },
            { from: 'error', to: 'end' }
        ];

        // Generar diagrama de flujo
        const flowChart = this.visualElements.createFlowChart(nodes, connections, {
            width: 800,
            height: 650
        });

        // Añadir explicación
        const explanation = `
        <!-- Explicación del diagrama -->
        <g class="explanation">
            <rect x="50" y="50" width="200" height="200" fill="#f8fafc" stroke="#e2e8f0" stroke-width="1" rx="8"/>
            <text x="60" y="75" class="subtitle" fill="#1e293b">Elementos del Flujo:</text>
            <text x="60" y="100" class="small-text" fill="#1e293b">🟢 Óvalo: Inicio/Fin</text>
            <text x="60" y="120" class="small-text" fill="#1e293b">🔷 Rombo: Decisión</text>
            <text x="60" y="140" class="small-text" fill="#1e293b">📦 Rectángulo: Proceso</text>
            <text x="60" y="160" class="small-text" fill="#1e293b">➡️ Flecha: Flujo</text>
            <text x="60" y="180" class="small-text" fill="#10b981">✅ Verde: Verdadero</text>
            <text x="60" y="200" class="small-text" fill="#ea580c">❌ Naranja: Falso</text>
        </g>`;

        return flowChart + explanation;
    }

    /**
     * Genera diagrama de flujo de ejecución de funciones
     * @returns {string} - SVG del contenido
     */
    generateExecutionFlowContent() {
        const nodes = [
            { id: 'main', type: 'start', label: 'main()', x: 350, y: 50 },
            { id: 'call1', type: 'process', label: 'llamar función A', x: 350, y: 150 },
            { id: 'funcA', type: 'process', label: 'función A()', x: 150, y: 250 },
            { id: 'call2', type: 'process', label: 'llamar función B', x: 150, y: 350 },
            { id: 'funcB', type: 'process', label: 'función B()', x: 350, y: 450 },
            { id: 'return2', type: 'process', label: 'retornar a A', x: 150, y: 450 },
            { id: 'return1', type: 'process', label: 'retornar a main', x: 350, y: 350 },
            { id: 'end', type: 'end', label: 'Fin', x: 350, y: 550 }
        ];

        const connections = [
            { from: 'main', to: 'call1' },
            { from: 'call1', to: 'funcA' },
            { from: 'funcA', to: 'call2' },
            { from: 'call2', to: 'funcB' },
            { from: 'funcB', to: 'return2' },
            { from: 'return2', to: 'return1' },
            { from: 'return1', to: 'end' }
        ];

        const flowChart = this.visualElements.createFlowChart(nodes, connections, {
            width: 600,
            height: 650
        });

        // Código de ejemplo
        const codeExample = this.createCodeBox(500, 150, 280, 200,
            'function main() {\n  console.log("Inicio");\n  funcionA();\n  console.log("Fin");\n}\n\nfunction funcionA() {\n  console.log("En A");\n  funcionB();\n  console.log("Saliendo de A");\n}\n\nfunction funcionB() {\n  console.log("En B");\n}'
        );

        return flowChart + codeExample;
    }

    /**
     * Genera diagrama de flujo condicional y bucles
     * @returns {string} - SVG del contenido
     */
    generateConditionalFlowContent() {
        const nodes = [
            { id: 'start', type: 'start', label: 'Inicio', x: 350, y: 50 },
            { id: 'init', type: 'process', label: 'i = 0', x: 350, y: 150 },
            { id: 'condition', type: 'decision', label: 'i < 5?', x: 350, y: 250 },
            { id: 'body', type: 'process', label: 'console.log(i)', x: 200, y: 350 },
            { id: 'increment', type: 'process', label: 'i++', x: 200, y: 450 },
            { id: 'end', type: 'end', label: 'Fin', x: 500, y: 350 }
        ];

        const connections = [
            { from: 'start', to: 'init' },
            { from: 'init', to: 'condition' },
            { from: 'condition', to: 'body', label: 'Sí', type: 'true' },
            { from: 'condition', to: 'end', label: 'No', type: 'false' },
            { from: 'body', to: 'increment' },
            { from: 'increment', to: 'condition' }
        ];

        const flowChart = this.visualElements.createFlowChart(nodes, connections, {
            width: 700,
            height: 550
        });

        // Añadir flecha de retorno del bucle
        const loopArrow = `
        <path d="M 200 470 Q 100 470 100 250 Q 100 230 330 250"
              stroke="#2563eb" stroke-width="2" fill="none"
              marker-end="url(#arrowhead-process)" stroke-dasharray="5,5"/>
        <text x="100" y="360" class="small-text" fill="#2563eb">Bucle</text>`;

        return flowChart + loopArrow;
    }

    // Métodos para generar ejemplos
    generateScopeExample() {
        return {
            svg: this.generate('scope'),
            description: 'Diagrama que explica el concepto de scope y hoisting en JavaScript, mostrando cómo las variables son accesibles en diferentes contextos.'
        };
    }

    generateArrowFunctionsExample() {
        return {
            svg: this.generate('arrow-functions'),
            description: 'Diagrama que compara las arrow functions con las funciones tradicionales, incluyendo sintaxis y comportamiento del contexto this.'
        };
    }

    generateDestructuringExample() {
        return {
            svg: this.generate('destructuring'),
            description: 'Diagrama que muestra cómo usar destructuring para extraer valores de arrays y objetos de manera eficiente.'
        };
    }

    generateSpreadOperatorExample() {
        return {
            svg: this.generate('spread-operator'),
            description: 'Diagrama que explica el uso del spread operator para expandir arrays y objetos.'
        };
    }

    generateArrayMethodsExample() {
        return {
            svg: this.generate('array-methods'),
            description: 'Diagrama que muestra visualmente cómo funcionan map(), filter() y reduce() con ejemplos prácticos.'
        };
    }

    generatePromisesExample() {
        return {
            svg: this.generate('promises'),
            description: 'Diagrama que explica las promesas en JavaScript, sus estados y cómo manejar operaciones asíncronas.'
        };
    }

    generateDOMExample() {
        return {
            svg: this.generate('dom'),
            description: 'Diagrama que muestra cómo manipular el DOM usando JavaScript, incluyendo selección y modificación de elementos.'
        };
    }

    generateEventsExample() {
        return {
            svg: this.generate('events'),
            description: 'Diagrama que explica el manejo de eventos en JavaScript, incluyendo event listeners y propagación de eventos.'
        };
    }
}

module.exports = IntermediateGenerator;
