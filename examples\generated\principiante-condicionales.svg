
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Condicionales (if/else)</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
        <!-- Definición -->
        
        <g class="box-group">
            <rect x="50" y="80" width="700" height="80" class="concept-box" rx="8" />
            <text x="65" y="105" class="subtitle">Definición</text>
            <text x="65" y="130" class="text">Las estructuras condicionales permiten ejecutar diferentes bloques de código</text>
<text x="65" y="148" class="text">dependiendo de si una condición es verdadera o falsa.</text>

        </g>
        
        <!-- if/else -->
        
        <g class="box-group">
            <rect x="50" y="180" width="320" height="150" class="concept-box" rx="8" />
            <text x="65" y="205" class="subtitle">if/else</text>
            <text x="65" y="230" class="text">if (condición) {
  // código si es</text>
<text x="65" y="248" class="text">verdadero
} else {
  // código si</text>
<text x="65" y="266" class="text">es falso
}</text>

        </g>
        
        <!-- if/else if/else -->
        
        <g class="box-group">
            <rect x="430" y="180" width="320" height="150" class="concept-box" rx="8" />
            <text x="445" y="205" class="subtitle">if/else if/else</text>
            <text x="445" y="230" class="text">if (condición1) {
  // código si</text>
<text x="445" y="248" class="text">condición1 es verdadera
} else if</text>
<text x="445" y="266" class="text">(condición2) {
  // código si</text>
<text x="445" y="284" class="text">condición2 es verdadera
} else {
 </text>
<text x="445" y="302" class="text">// código si ninguna es</text>
<text x="445" y="320" class="text">verdadera
}</text>

        </g>
        
        <!-- Operador ternario -->
        
        <g class="box-group">
            <rect x="50" y="350" width="320" height="100" class="concept-box" rx="8" />
            <text x="65" y="375" class="subtitle">Operador ternario</text>
            <text x="65" y="400" class="text">condición ? valorSiVerdadero :</text>
<text x="65" y="418" class="text">valorSiFalso</text>

        </g>
        
        <!-- Switch -->
        
        <g class="box-group">
            <rect x="430" y="350" width="320" height="150" class="concept-box" rx="8" />
            <text x="445" y="375" class="subtitle">Switch</text>
            <text x="445" y="400" class="text">switch(expresión) {
  case</text>
<text x="445" y="418" class="text">valor1:
    // código
    break;
 </text>
<text x="445" y="436" class="text">case valor2:
    // código
   </text>
<text x="445" y="454" class="text">break;
  default:
    // código</text>
<text x="445" y="472" class="text">por defecto
}</text>

        </g>
        
        <!-- Ejemplo práctico -->
        
        <g class="code-group">
            <rect x="50" y="520" width="700" height="120" class="code-box" rx="4" />
            <text x="65" y="545" class="code">let edad = 18;</text>
<text x="65" y="561" class="code"></text>
<text x="65" y="577" class="code">if (edad &gt;= 18) {</text>
<text x="65" y="593" class="code">  console.log(&quot;Eres mayor de edad&quot;);</text>
<text x="65" y="609" class="code">} else {</text>
<text x="65" y="625" class="code">  console.log(&quot;Eres menor de edad&quot;);</text>
<text x="65" y="641" class="code">}</text>
<text x="65" y="657" class="code"></text>
<text x="65" y="673" class="code">// Usando operador ternario</text>
<text x="65" y="689" class="code">let mensaje = edad &gt;= 18 ? &quot;Mayor de edad&quot; : &quot;Menor de edad&quot;;</text>

        </g>
        
    </g>
</svg>