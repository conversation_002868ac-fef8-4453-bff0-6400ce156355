
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
        <marker id="arrowhead-normal" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
        </marker>
        <marker id="arrowhead-true" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
        </marker>
        <marker id="arrowhead-false" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#ea580c" />
        </marker>
        <marker id="arrowhead-process" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#2563eb" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Contexto de ejecución visual</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
            <rect x="100" y="100" width="200" height="80"
                  fill="undefined" fill-opacity="0.1" stroke="undefined" stroke-width="2" rx="8"/>
            <text x="110" y="125" class="subtitle" fill="undefined">
                Global Context
            </text>
            <text x="110" y="145" class="small-text" fill="#1e293b">
                Variables: globalVar
            </text>
            <text x="110" y="165" class="small-text" fill="#1e293b">
                Scope Chain: Level 0
            </text>
            <rect x="100" y="190" width="200" height="80"
                  fill="undefined" fill-opacity="0.1" stroke="undefined" stroke-width="2" rx="8"/>
            <text x="110" y="215" class="subtitle" fill="undefined">
                outerFunction()
            </text>
            <text x="110" y="235" class="small-text" fill="#1e293b">
                Variables: localVar
            </text>
            <text x="110" y="255" class="small-text" fill="#1e293b">
                Scope Chain: Level 0 → Level 1
            </text>
            <rect x="100" y="280" width="200" height="80"
                  fill="#2563eb" fill-opacity="0.2" stroke="#2563eb" stroke-width="2" rx="8"/>
            <text x="110" y="305" class="subtitle" fill="#2563eb">
                innerFunction()
            </text>
            <text x="110" y="325" class="small-text" fill="#1e293b">
                Variables: blockVar
            </text>
            <text x="110" y="345" class="small-text" fill="#1e293b">
                Scope Chain: Level 0 → Level 1 → Level 2
            </text>
        <polygon points="350,320 370,310 370,330"
                 fill="#2563eb"/>
        <text x="380" y="325" class="small-text" fill="#2563eb">
            Contexto Actual
        </text>
        <g class="code-group">
            <rect x="400" y="100" width="350" height="180" class="code-box" rx="4" />
            <text x="415" y="125" class="code">let globalVar = &quot;global&quot;;</text>
<text x="415" y="141" class="code"></text>
<text x="415" y="157" class="code">function outerFunction() {</text>
<text x="415" y="173" class="code">  let localVar = &quot;local&quot;;</text>
<text x="415" y="189" class="code">  </text>
<text x="415" y="205" class="code">  function innerFunction() {</text>
<text x="415" y="221" class="code">    let blockVar = &quot;block&quot;;</text>
<text x="415" y="237" class="code">    // Contexto actual: innerFunction</text>
<text x="415" y="253" class="code">    console.log(blockVar, localVar, globalVar);</text>
<text x="415" y="269" class="code">  }</text>
<text x="415" y="285" class="code">  </text>
<text x="415" y="301" class="code">  innerFunction(); // ← Aquí</text>
<text x="415" y="317" class="code">}</text>

        </g>
        <g class="box-group">
            <rect x="400" y="300" width="350" height="150" class="concept-box" rx="8" />
            <text x="415" y="325" class="subtitle">Call Stack</text>
            <text x="415" y="350" class="text">Cada llamada a función crea un nuevo</text>
<text x="415" y="368" class="text">contexto de ejecución que se apila. El</text>
<text x="415" y="386" class="text">contexto actual está en la cima del</text>
<text x="415" y="404" class="text">stack. Al retornar, el contexto se</text>
<text x="415" y="422" class="text">elimina.</text>

        </g>
    </g>
</svg>