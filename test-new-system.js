const DiagramGenerator = require('./src/generators/DiagramGenerator');
const fs = require('fs');

// Crear instancia del generador
const generator = new DiagramGenerator();

console.log('🧪 Probando sistema mejorado...\n');

// Probar nuevo tema de objetos
try {
    console.log('📦 Generando diagrama de objetos...');
    const objetosSVG = generator.generateDiagram('objetos', 'principiante');
    
    // Guardar archivo
    fs.writeFileSync('./test-objetos-mejorado.svg', objetosSVG, 'utf8');
    console.log('✅ Diagrama de objetos generado y guardado como test-objetos-mejorado.svg');
    
    // Verificar que el SVG contiene el nuevo contenido
    if (objetosSVG.includes('Definición') && objetosSVG.includes('Creación de objetos')) {
        console.log('✅ El diagrama contiene el contenido esperado');
    } else {
        console.log('❌ El diagrama no contiene el contenido esperado');
    }
    
} catch (error) {
    console.log('❌ Error generando diagrama de objetos:', error.message);
}

// Probar tema de JSON
try {
    console.log('\n📄 Generando diagrama de JSON...');
    const jsonSVG = generator.generateDiagram('json', 'principiante');
    
    // Guardar archivo
    fs.writeFileSync('./test-json-mejorado.svg', jsonSVG, 'utf8');
    console.log('✅ Diagrama de JSON generado y guardado como test-json-mejorado.svg');
    
} catch (error) {
    console.log('❌ Error generando diagrama de JSON:', error.message);
}

// Probar tema de Math
try {
    console.log('\n🔢 Generando diagrama de Math...');
    const mathSVG = generator.generateDiagram('math', 'principiante');
    
    // Guardar archivo
    fs.writeFileSync('./test-math-mejorado.svg', mathSVG, 'utf8');
    console.log('✅ Diagrama de Math generado y guardado como test-math-mejorado.svg');
    
} catch (error) {
    console.log('❌ Error generando diagrama de Math:', error.message);
}

// Mostrar estadísticas
console.log('\n📊 Estadísticas del sistema:');
const levels = generator.getAvailableLevels();
levels.forEach(level => {
    console.log(`${level.name}: ${level.topics.length} temas`);
});

const totalTopics = levels.reduce((total, level) => total + level.topics.length, 0);
console.log(`\n🎯 Total de temas disponibles: ${totalTopics}`);

console.log('\n🎉 Pruebas del sistema mejorado completadas!');
