
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Matemáticas en JavaScript</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
        <g class="box-group">
            <rect x="50" y="80" width="400" height="124" class="concept-box" rx="8" />
            <text x="65" y="105" class="subtitle">Definición</text>
            <text x="65" y="130" class="text">El objeto Math proporciona propiedades y</text>
<text x="65" y="148" class="text">métodos para constantes y funciones</text>
<text x="65" y="166" class="text">matemáticas. No es un constructor.</text>

        </g>
        <g class="box-group">
            <rect x="470" y="80" width="350" height="124" class="concept-box" rx="8" />
            <text x="485" y="105" class="subtitle">Constantes Math</text>
            <text x="485" y="130" class="text">Math.PI: π (3.14159...) - Math.E: e</text>
<text x="485" y="148" class="text">(2.718...) - Math.LN2: ln(2) -</text>
<text x="485" y="166" class="text">Math.SQRT2: √2</text>

        </g>
        <g class="box-group">
            <rect x="50" y="224" width="400" height="124" class="concept-box" rx="8" />
            <text x="65" y="249" class="subtitle">Métodos básicos</text>
            <text x="65" y="274" class="text">Math.abs(): Valor absoluto - Math.round():</text>
<text x="65" y="292" class="text">Redondear - Math.floor(): Redondear hacia</text>
<text x="65" y="310" class="text">abajo - Math.ceil(): Redondear hacia arriba</text>

        </g>
        <g class="box-group">
            <rect x="470" y="224" width="400" height="142" class="concept-box" rx="8" />
            <text x="485" y="249" class="subtitle">Métodos avanzados</text>
            <text x="485" y="274" class="text">Math.random(): Número aleatorio (0-1) -</text>
<text x="485" y="292" class="text">Math.max(): Máximo - Math.min(): Mínimo -</text>
<text x="485" y="310" class="text">Math.pow(): Potencia - Math.sqrt(): Raíz</text>
<text x="485" y="328" class="text">cuadrada</text>

        </g>
        <g class="code-group">
            <rect x="50" y="386" width="450" height="214" class="code-box" rx="4" />
            <text x="65" y="411" class="code">// Operaciones básicas</text>
<text x="65" y="427" class="code">console.log(Math.abs(-5));    // 5</text>
<text x="65" y="443" class="code">console.log(Math.round(4.7)); // 5</text>
<text x="65" y="459" class="code">console.log(Math.floor(4.7)); // 4</text>
<text x="65" y="475" class="code">console.log(Math.ceil(4.1));  // 5</text>
<text x="65" y="491" class="code"></text>
<text x="65" y="507" class="code">// Números aleatorios</text>
<text x="65" y="523" class="code">let aleatorio = Math.random(); // 0.0 - 0.999...</text>
<text x="65" y="539" class="code">let dado = Math.floor(Math.random() * 6) + 1; // 1-6</text>
<text x="65" y="555" class="code"></text>
<text x="65" y="571" class="code">// Máximo y mínimo</text>
<text x="65" y="587" class="code">console.log(Math.max(1, 5, 3)); // 5</text>

        </g>
    </g>
</svg>