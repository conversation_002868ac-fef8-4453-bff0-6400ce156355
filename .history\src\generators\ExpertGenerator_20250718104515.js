const BaseGenerator = require('./BaseGenerator');

class ExpertGenerator extends BaseGenerator {
    constructor() {
        super();
        
        // Definir temas disponibles para nivel experto
        this.topics = {
            'event-loop': 'Event loop y call stack',
            'memory-management': 'Memory management',
            'microtasks': 'Micro/macro tasks',
            'advanced-patterns': 'Advanced patterns (Observer, Factory, etc.)',
            'metaprogramming': 'Metaprogramming',
            'typescript': 'TypeScript integration',
            'performance-profiling': 'Performance profiling',
            'v8-internals': 'V8 Engine internals',
            'compilation': 'JIT compilation',
            'garbage-collection': 'Garbage collection avanzado',
            'web-workers': 'Web Workers y threading',
            'shared-array-buffer': 'SharedArrayBuffer y Atomics',
            'wasm-integration': 'WebAssembly integration',
            'node-internals': 'Node.js internals',
            'security': 'Seguridad en JavaScript',
            'frameworks-internals': 'Internals de frameworks',
            'build-tools': 'Build tools avanzados',
            'micro-frontends': 'Micro frontends',
            'pwa-advanced': 'PWA avanzado',
            'event-loop-diagram': 'Diagrama detallado del Event Loop',
            'memory-model': 'Modelo de memoria visual',
            'execution-timeline': 'Línea de tiempo de ejecución',
            'async-flow': 'Flujo asíncrono visual'
        };
        
        // Ejemplos predefinidos
        this.examples = {
            'event-loop': this.generateEventLoopExample(),
            'memory-management': this.generateMemoryManagementExample(),
            'microtasks': this.generateMicrotasksExample(),
            'advanced-patterns': this.generateAdvancedPatternsExample(),
            'metaprogramming': this.generateMetaprogrammingExample(),
            'typescript': this.generateTypeScriptExample(),
            'performance-profiling': this.generatePerformanceProfilingExample()
        };
    }

    /**
     * Genera un diagrama SVG para un tema de nivel experto
     * @param {string} topic - Tema a generar
     * @param {object} options - Opciones adicionales
     * @returns {string} - SVG generado
     */
    generate(topic, options = {}) {
        const normalizedTopic = topic.toLowerCase();
        
        if (!this.hasTopicSupport(normalizedTopic)) {
            throw new Error(`El tema "${topic}" no está soportado para nivel experto`);
        }
        
        // Obtener el título completo del tema
        const title = this.topics[normalizedTopic];
        
        // Generar el SVG base con dimensiones más grandes para contenido complejo
        const svgOptions = { width: 1200, height: 900, ...options };
        let svg = this.generateBaseSVG(title, svgOptions);
        
        // Generar el contenido específico según el tema
        let content = '';
        switch (normalizedTopic) {
            case 'event-loop':
                content = this.generateEventLoopContent();
                break;
            case 'memory-management':
                content = this.generateMemoryManagementContent();
                break;
            case 'microtasks':
                content = this.generateMicrotasksContent();
                break;
            case 'advanced-patterns':
                content = this.generateAdvancedPatternsContent();
                break;
            case 'metaprogramming':
                content = this.generateMetaprogrammingContent();
                break;
            case 'typescript':
                content = this.generateTypeScriptContent();
                break;
            case 'performance-profiling':
                content = this.generatePerformanceProfilingContent();
                break;
            case 'event-loop-diagram':
                content = this.generateEventLoopDiagramContent();
                break;
            case 'memory-model':
                content = this.generateMemoryModelContent();
                break;
            case 'execution-timeline':
                content = this.generateExecutionTimelineContent();
                break;
            case 'async-flow':
                content = this.generateAsyncFlowContent();
                break;
            default:
                content = '<text x="600" y="450" text-anchor="middle" class="text">Contenido no disponible</text>';
        }
        
        // Insertar el contenido en el SVG
        svg = svg.replace('<!-- El contenido específico se añadirá aquí -->', content);
        
        return svg;
    }

    /**
     * Genera el contenido para el tema de event loop
     * @returns {string} - SVG del contenido
     */
    generateEventLoopContent() {
        return `
        <!-- Fundamentos teóricos -->
        ${this.createBox(50, 80, 1100, 80, 'Fundamentos teóricos', 'JavaScript es single-threaded pero puede manejar operaciones asíncronas gracias al Event Loop. El Event Loop coordina la ejecución entre el Call Stack, Callback Queue, y Microtask Queue.', 'concept')}
        
        <!-- Componentes del motor JS -->
        ${this.createBox(50, 180, 350, 180, 'Componentes del motor JS', 'Call Stack: Pila de ejecución\nHeap: Memoria para objetos\nCallback Queue: Cola de callbacks\nMicrotask Queue: Cola de microtareas\nEvent Loop: Coordinador\nWeb APIs: setTimeout, fetch, etc.', 'concept')}
        
        <!-- Diagrama de flujo -->
        <g>
            <!-- Call Stack -->
            <rect x="450" y="180" width="200" height="180" fill="#e2e8f0" stroke="#2563eb" stroke-width="2" />
            <text x="550" y="200" text-anchor="middle" class="subtitle">Call Stack</text>
            <rect x="470" y="220" width="160" height="30" fill="#bfdbfe" stroke="#2563eb" />
            <text x="550" y="240" text-anchor="middle" class="small-text">main()</text>
            <rect x="470" y="260" width="160" height="30" fill="#bfdbfe" stroke="#2563eb" />
            <text x="550" y="280" text-anchor="middle" class="small-text">function1()</text>
            <rect x="470" y="300" width="160" height="30" fill="#bfdbfe" stroke="#2563eb" />
            <text x="550" y="320" text-anchor="middle" class="small-text">function2()</text>
            
            <!-- Web APIs -->
            <rect x="700" y="180" width="200" height="180" fill="#e2e8f0" stroke="#16a34a" stroke-width="2" />
            <text x="800" y="200" text-anchor="middle" class="subtitle">Web APIs</text>
            <rect x="720" y="220" width="160" height="30" fill="#dcfce7" stroke="#16a34a" />
            <text x="800" y="240" text-anchor="middle" class="small-text">setTimeout()</text>
            <rect x="720" y="260" width="160" height="30" fill="#dcfce7" stroke="#16a34a" />
            <text x="800" y="280" text-anchor="middle" class="small-text">fetch()</text>
            <rect x="720" y="300" width="160" height="30" fill="#dcfce7" stroke="#16a34a" />
            <text x="800" y="320" text-anchor="middle" class="small-text">addEventListener()</text>
            
            <!-- Callback Queue -->
            <rect x="450" y="400" width="200" height="100" fill="#e2e8f0" stroke="#ea580c" stroke-width="2" />
            <text x="550" y="420" text-anchor="middle" class="subtitle">Callback Queue</text>
            <rect x="470" y="440" width="160" height="30" fill="#fed7aa" stroke="#ea580c" />
            <text x="550" y="460" text-anchor="middle" class="small-text">setTimeout callback</text>
            
            <!-- Microtask Queue -->
            <rect x="700" y="400" width="200" height="100" fill="#e2e8f0" stroke="#8b5cf6" stroke-width="2" />
            <text x="800" y="420" text-anchor="middle" class="subtitle">Microtask Queue</text>
            <rect x="720" y="440" width="160" height="30" fill="#ddd6fe" stroke="#8b5cf6" />
            <text x="800" y="460" text-anchor="middle" class="small-text">Promise callback</text>
            
            <!-- Event Loop -->
            <ellipse cx="600" cy="550" rx="80" ry="40" fill="#f8fafc" stroke="#1e293b" stroke-width="2" />
            <text x="600" y="555" text-anchor="middle" class="subtitle">Event Loop</text>
            
            <!-- Flechas -->
            ${this.createArrow(550, 360, 550, 400, '')}
            ${this.createArrow(800, 360, 800, 400, '')}
            ${this.createArrow(650, 180, 700, 180, '')}
            ${this.createArrow(700, 360, 650, 360, '')}
            ${this.createArrow(550, 500, 550, 550, '')}
            ${this.createArrow(800, 500, 650, 550, '')}
            ${this.createArrow(550, 550, 550, 500, '')}
        </g>
        
        <!-- Ejemplo de código -->
        ${this.createCodeBox(50, 520, 350, 180, 'console.log("Start");\n\nsetTimeout(() => {\n  console.log("Timeout");\n}, 0);\n\nPromise.resolve().then(() => {\n  console.log("Promise");\n});\n\nconsole.log("End");\n\n// Output: Start, End, Promise, Timeout')}
        
        <!-- Análisis de rendimiento -->
        ${this.createBox(50, 720, 1100, 80, 'Análisis de rendimiento', 'Bloquear el Event Loop causa jank (UI no responde). Las tareas largas deben dividirse. Las microtareas tienen prioridad sobre macrotareas. Usar requestAnimationFrame para animaciones eficientes.', 'warning')}
        
        <!-- Casos edge -->
        ${this.createBox(50, 820, 530, 60, 'Casos edge', 'Anidación de promesas. Recursión en microtareas. Interacción con requestAnimationFrame. Prioridad entre diferentes tipos de eventos.', 'warning')}
        
        <!-- Tendencias futuras -->
        ${this.createBox(620, 820, 530, 60, 'Tendencias futuras', 'Web Workers. SharedArrayBuffer. Atomics API. Top-level await. Scheduler API.', 'example')}
        `;
    }

    // Métodos para generar contenido de otros temas (implementación básica)
    generateMemoryManagementContent() {
        return `<!-- Contenido para memory management -->`;
    }

    generateMicrotasksContent() {
        return `<!-- Contenido para microtasks -->`;
    }

    generateAdvancedPatternsContent() {
        return `<!-- Contenido para advanced patterns -->`;
    }

    generateMetaprogrammingContent() {
        return `<!-- Contenido para metaprogramming -->`;
    }

    generateTypeScriptContent() {
        return `<!-- Contenido para TypeScript -->`;
    }

    generatePerformanceProfilingContent() {
        return `<!-- Contenido para performance profiling -->`;
    }

    /**
     * Genera diagrama detallado del Event Loop
     * @returns {string} - SVG del contenido
     */
    generateEventLoopDiagramContent() {
        // Crear componentes del Event Loop con posiciones específicas
        const components = [
            { name: 'Call Stack', x: 100, y: 100, width: 150, height: 200, type: 'stack' },
            { name: 'Heap', x: 300, y: 100, width: 150, height: 100, type: 'heap' },
            { name: 'Web APIs', x: 500, y: 100, width: 150, height: 150, type: 'apis' },
            { name: 'Callback Queue', x: 300, y: 300, width: 200, height: 80, type: 'queue' },
            { name: 'Microtask Queue', x: 300, y: 400, width: 200, height: 80, type: 'microtask' },
            { name: 'Event Loop', x: 550, y: 350, width: 100, height: 60, type: 'loop' }
        ];

        let svg = '';

        // Dibujar componentes
        components.forEach(comp => {
            const color = this.getComponentColor(comp.type);
            svg += `
            <rect x="${comp.x}" y="${comp.y}" width="${comp.width}" height="${comp.height}"
                  fill="${color}" fill-opacity="0.1" stroke="${color}" stroke-width="2" rx="8"/>
            <text x="${comp.x + comp.width/2}" y="${comp.y + 25}" text-anchor="middle"
                  class="subtitle" fill="${color}">${comp.name}</text>`;

            // Añadir contenido específico
            if (comp.type === 'stack') {
                svg += `
                <rect x="${comp.x + 10}" y="${comp.y + 40}" width="${comp.width - 20}" height="30"
                      fill="${color}" fill-opacity="0.3" rx="4"/>
                <text x="${comp.x + comp.width/2}" y="${comp.y + 60}" text-anchor="middle"
                      class="small-text">main()</text>
                <rect x="${comp.x + 10}" y="${comp.y + 80}" width="${comp.width - 20}" height="30"
                      fill="${color}" fill-opacity="0.3" rx="4"/>
                <text x="${comp.x + comp.width/2}" y="${comp.y + 100}" text-anchor="middle"
                      class="small-text">setTimeout()</text>`;
            } else if (comp.type === 'apis') {
                svg += `
                <text x="${comp.x + 10}" y="${comp.y + 50}" class="small-text">• setTimeout</text>
                <text x="${comp.x + 10}" y="${comp.y + 70}" class="small-text">• fetch</text>
                <text x="${comp.x + 10}" y="${comp.y + 90}" class="small-text">• DOM events</text>`;
            } else if (comp.type === 'queue') {
                svg += `
                <rect x="${comp.x + 10}" y="${comp.y + 30}" width="60" height="30"
                      fill="${color}" fill-opacity="0.3" rx="4"/>
                <text x="${comp.x + 40}" y="${comp.y + 50}" text-anchor="middle"
                      class="small-text">callback</text>`;
            } else if (comp.type === 'microtask') {
                svg += `
                <rect x="${comp.x + 10}" y="${comp.y + 30}" width="60" height="30"
                      fill="${color}" fill-opacity="0.3" rx="4"/>
                <text x="${comp.x + 40}" y="${comp.y + 50}" text-anchor="middle"
                      class="small-text">promise</text>`;
            }
        });

        // Flechas de flujo
        const arrows = [
            { from: { x: 250, y: 150 }, to: { x: 300, y: 150 }, label: 'allocate' },
            { from: { x: 250, y: 200 }, to: { x: 500, y: 150 }, label: 'async call' },
            { from: { x: 575, y: 250 }, to: { x: 400, y: 300 }, label: 'callback' },
            { from: { x: 500, y: 340 }, to: { x: 550, y: 360 }, label: 'check' },
            { from: { x: 550, y: 380 }, to: { x: 200, y: 300 }, label: 'push to stack', curve: true }
        ];

        arrows.forEach(arrow => {
            if (arrow.curve) {
                svg += `
                <path d="M ${arrow.from.x} ${arrow.from.y} Q ${arrow.from.x - 100} ${arrow.from.y + 50} ${arrow.to.x} ${arrow.to.y}"
                      stroke="${this.colors.primary}" stroke-width="2" fill="none"
                      marker-end="url(#arrowhead)" stroke-dasharray="5,5"/>`;
            } else {
                svg += `
                <line x1="${arrow.from.x}" y1="${arrow.from.y}" x2="${arrow.to.x}" y2="${arrow.to.y}"
                      stroke="${this.colors.primary}" stroke-width="2" marker-end="url(#arrowhead)"/>`;
            }

            const midX = (arrow.from.x + arrow.to.x) / 2;
            const midY = (arrow.from.y + arrow.to.y) / 2 - 10;
            svg += `<text x="${midX}" y="${midY}" text-anchor="middle" class="small-text"
                   fill="${this.colors.primary}">${arrow.label}</text>`;
        });

        // Código de ejemplo
        const codeExample = this.createCodeBox(50, 520, 600, 120,
            'console.log("1");\nsetTimeout(() => console.log("2"), 0);\nPromise.resolve().then(() => console.log("3"));\nconsole.log("4");\n\n// Output: 1, 4, 3, 2 (microtasks tienen prioridad)'
        );

        return svg + codeExample;
    }

    /**
     * Obtiene color para componentes del Event Loop
     */
    getComponentColor(type) {
        const colors = {
            'stack': this.colors.primary,
            'heap': this.colors.secondary,
            'apis': this.colors.info,
            'queue': this.colors.warning,
            'microtask': this.colors.purple,
            'loop': this.colors.success
        };
        return colors[type] || this.colors.gray;
    }

    /**
     * Genera modelo de memoria visual
     * @returns {string} - SVG del contenido
     */
    generateMemoryModelContent() {
        let svg = '';

        // Stack Memory
        svg += `
        <rect x="100" y="100" width="200" height="300" fill="${this.colors.primary}"
              fill-opacity="0.1" stroke="${this.colors.primary}" stroke-width="2" rx="8"/>
        <text x="200" y="125" text-anchor="middle" class="subtitle" fill="${this.colors.primary}">
            Stack Memory
        </text>`;

        // Stack frames
        const stackFrames = [
            { name: 'main()', vars: ['x: 5', 'y: 10'] },
            { name: 'calculate()', vars: ['a: 5', 'b: 10', 'result: ref→'] },
            { name: 'helper()', vars: ['temp: 15'] }
        ];

        stackFrames.forEach((frame, index) => {
            const y = 350 - index * 70;
            svg += `
            <rect x="110" y="${y}" width="180" height="60" fill="${this.colors.primary}"
                  fill-opacity="0.2" stroke="${this.colors.primary}" stroke-width="1" rx="4"/>
            <text x="120" y="${y + 20}" class="small-text" fill="${this.colors.primary}">
                ${frame.name}
            </text>`;

            frame.vars.forEach((variable, varIndex) => {
                svg += `
                <text x="120" y="${y + 35 + varIndex * 12}" class="small-text" fill="${this.colors.text}">
                    ${variable}
                </text>`;
            });
        });

        // Heap Memory
        svg += `
        <rect x="400" y="100" width="300" height="300" fill="${this.colors.secondary}"
              fill-opacity="0.1" stroke="${this.colors.secondary}" stroke-width="2" rx="8"/>
        <text x="550" y="125" text-anchor="middle" class="subtitle" fill="${this.colors.secondary}">
            Heap Memory
        </text>`;

        // Objects in heap
        const heapObjects = [
            { name: 'Object @0x1234', props: ['name: "John"', 'age: 30'], x: 420, y: 150 },
            { name: 'Array @0x5678', props: ['[0]: 1', '[1]: 2', '[2]: 3'], x: 420, y: 250 },
            { name: 'Function @0x9ABC', props: ['code: {...}', 'scope: ref→'], x: 550, y: 200 }
        ];

        heapObjects.forEach(obj => {
            svg += `
            <rect x="${obj.x}" y="${obj.y}" width="120" height="80" fill="${this.colors.secondary}"
                  fill-opacity="0.2" stroke="${this.colors.secondary}" stroke-width="1" rx="4"/>
            <text x="${obj.x + 5}" y="${obj.y + 15}" class="small-text" fill="${this.colors.secondary}">
                ${obj.name}
            </text>`;

            obj.props.forEach((prop, propIndex) => {
                svg += `
                <text x="${obj.x + 5}" y="${obj.y + 30 + propIndex * 12}" class="small-text"
                      fill="${this.colors.text}">${prop}</text>`;
            });
        });

        // Reference arrow
        svg += `
        <line x1="290" y1="320" x2="420" y2="190" stroke="${this.colors.warning}"
              stroke-width="2" marker-end="url(#arrowhead)" stroke-dasharray="5,5"/>
        <text x="350" y="250" class="small-text" fill="${this.colors.warning}">reference</text>`;

        // Garbage Collection area
        svg += `
        <rect x="750" y="100" width="150" height="150" fill="${this.colors.gray}"
              fill-opacity="0.1" stroke="${this.colors.gray}" stroke-width="2" rx="8"/>
        <text x="825" y="125" text-anchor="middle" class="subtitle" fill="${this.colors.gray}">
            GC Queue
        </text>
        <text x="760" y="150" class="small-text" fill="${this.colors.gray}">Unreachable:</text>
        <text x="760" y="170" class="small-text" fill="${this.colors.gray}">• Object @0xDEAD</text>
        <text x="760" y="190" class="small-text" fill="${this.colors.gray}">• Array @0xBEEF</text>`;

        return svg;
    }

    /**
     * Genera línea de tiempo de ejecución
     * @returns {string} - SVG del contenido
     */
    generateExecutionTimelineContent() {
        let svg = '';

        // Timeline base
        svg += `
        <line x1="100" y1="200" x2="900" y2="200" stroke="${this.colors.text}" stroke-width="2"/>
        <text x="500" y="190" text-anchor="middle" class="subtitle">Línea de Tiempo de Ejecución</text>`;

        // Time markers
        const timeMarkers = [0, 5, 10, 15, 20, 25, 30];
        timeMarkers.forEach(time => {
            const x = 100 + (time / 30) * 800;
            svg += `
            <line x1="${x}" y1="195" x2="${x}" y2="205" stroke="${this.colors.text}" stroke-width="1"/>
            <text x="${x}" y="220" text-anchor="middle" class="small-text">${time}ms</text>`;
        });

        // Execution events
        const events = [
            { time: 0, type: 'sync', label: 'console.log("start")', duration: 1 },
            { time: 2, type: 'async', label: 'setTimeout(cb, 10)', duration: 1 },
            { time: 4, type: 'sync', label: 'Promise.resolve()', duration: 1 },
            { time: 6, type: 'microtask', label: 'promise.then()', duration: 2 },
            { time: 10, type: 'sync', label: 'console.log("end")', duration: 1 },
            { time: 12, type: 'callback', label: 'setTimeout callback', duration: 2 }
        ];

        events.forEach(event => {
            const x = 100 + (event.time / 30) * 800;
            const width = (event.duration / 30) * 800;
            const color = this.getEventColor(event.type);
            const y = this.getEventY(event.type);

            svg += `
            <rect x="${x}" y="${y}" width="${width}" height="20" fill="${color}"
                  fill-opacity="0.7" stroke="${color}" stroke-width="1" rx="2"/>
            <text x="${x + width/2}" y="${y + 15}" text-anchor="middle" class="small-text"
                  fill="white">${event.label}</text>`;
        });

        // Legend
        const legend = [
            { type: 'sync', label: 'Synchronous' },
            { type: 'async', label: 'Async API Call' },
            { type: 'microtask', label: 'Microtask' },
            { type: 'callback', label: 'Callback' }
        ];

        legend.forEach((item, index) => {
            const x = 100 + index * 150;
            const color = this.getEventColor(item.type);
            svg += `
            <rect x="${x}" y="350" width="20" height="15" fill="${color}" rx="2"/>
            <text x="${x + 30}" y="362" class="small-text">${item.label}</text>`;
        });

        return svg;
    }

    /**
     * Genera flujo asíncrono visual
     * @returns {string} - SVG del contenido
     */
    generateAsyncFlowContent() {
        const nodes = [
            { id: 'start', type: 'start', label: 'Inicio', x: 100, y: 100 },
            { id: 'fetch', type: 'process', label: 'fetch(url)', x: 100, y: 200 },
            { id: 'pending', type: 'decision', label: 'Promise\nPending', x: 300, y: 200 },
            { id: 'success', type: 'process', label: 'then()', x: 200, y: 350 },
            { id: 'error', type: 'process', label: 'catch()', x: 400, y: 350 },
            { id: 'finally', type: 'process', label: 'finally()', x: 300, y: 450 },
            { id: 'end', type: 'end', label: 'Fin', x: 300, y: 550 }
        ];

        const connections = [
            { from: 'start', to: 'fetch' },
            { from: 'fetch', to: 'pending' },
            { from: 'pending', to: 'success', label: 'Resolved', type: 'true' },
            { from: 'pending', to: 'error', label: 'Rejected', type: 'false' },
            { from: 'success', to: 'finally' },
            { from: 'error', to: 'finally' },
            { from: 'finally', to: 'end' }
        ];

        const flowChart = this.visualElements.createFlowChart(nodes, connections, {
            width: 600,
            height: 650
        });

        // Código de ejemplo
        const codeExample = this.createCodeBox(500, 150, 300, 200,
            'async function fetchData() {\n  try {\n    const response = await fetch(url);\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error(error);\n  } finally {\n    console.log("Cleanup");\n  }\n}'
        );

        return flowChart + codeExample;
    }

    // Helper methods
    getEventColor(type) {
        const colors = {
            'sync': this.colors.primary,
            'async': this.colors.warning,
            'microtask': this.colors.purple,
            'callback': this.colors.secondary
        };
        return colors[type] || this.colors.gray;
    }

    getEventY(type) {
        const positions = {
            'sync': 160,
            'async': 140,
            'microtask': 120,
            'callback': 180
        };
        return positions[type] || 160;
    }

    // Métodos para generar ejemplos
    generateEventLoopExample() {
        return {
            svg: this.generate('event-loop'),
            description: 'Diagrama experto que explica el Event Loop en JavaScript, incluyendo call stack, heap, callback queue, microtask queue y el flujo completo de ejecución.'
        };
    }

    generateMemoryManagementExample() {
        return {
            svg: this.generate('memory-management'),
            description: 'Diagrama que explica cómo JavaScript gestiona la memoria, incluyendo garbage collection y cómo evitar memory leaks.'
        };
    }

    generateMicrotasksExample() {
        return {
            svg: this.generate('microtasks'),
            description: 'Diagrama que explica la diferencia entre microtasks y macrotasks, y cómo afectan al orden de ejecución.'
        };
    }

    generateAdvancedPatternsExample() {
        return {
            svg: this.generate('advanced-patterns'),
            description: 'Diagrama que muestra patrones de diseño avanzados en JavaScript y sus implementaciones.'
        };
    }

    generateMetaprogrammingExample() {
        return {
            svg: this.generate('metaprogramming'),
            description: 'Diagrama que explica técnicas de metaprogramación en JavaScript como Proxies, Reflect API y Symbol.'
        };
    }

    generateTypeScriptExample() {
        return {
            svg: this.generate('typescript'),
            description: 'Diagrama que muestra cómo integrar TypeScript con JavaScript y sus beneficios.'
        };
    }

    generatePerformanceProfilingExample() {
        return {
            svg: this.generate('performance-profiling'),
            description: 'Diagrama que explica cómo realizar profiling de rendimiento en aplicaciones JavaScript.'
        };
    }
}

module.exports = ExpertGenerator;
