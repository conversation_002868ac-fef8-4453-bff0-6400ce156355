
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Closures y lexical scope</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
        <!-- Conceptos fundamentales -->
        
        <g class="box-group">
            <rect x="50" y="80" width="1100" height="80" class="warning-box" rx="8" />
            <text x="65" y="105" class="subtitle">Conceptos fundamentales requeridos</text>
            <text x="65" y="130" class="text">Scope, Lexical scope, Funciones como first-class objects, Execution context, Call stack</text>
        </g>
        
        <!-- Definición técnica -->
        
        <g class="box-group">
            <rect x="50" y="180" width="530" height="120" class="concept-box" rx="8" />
            <text x="65" y="205" class="subtitle">Definición técnica</text>
            <text x="65" y="230" class="text">Un closure es la combinación de una función y el entorno léxico en el que fue declarada. Permite que una función acceda a variables de su scope externo incluso después de que la función externa haya terminado de ejecutarse.</text>
        </g>
        
        <!-- Scope chain -->
        
        <g class="box-group">
            <rect x="620" y="180" width="530" height="120" class="concept-box" rx="8" />
            <text x="635" y="205" class="subtitle">Scope Chain</text>
            <text x="635" y="230" class="text">JavaScript busca variables siguiendo la cadena de scope:
1. Scope local de la función
2. Scope de la función contenedora
3. Scope global

Los closures mantienen esta cadena activa.</text>
        </g>
        
        <!-- Ejemplo básico -->
        
        <g class="code-group">
            <rect x="50" y="320" width="530" height="150" class="code-box" rx="4" />
            <text x="65" y="345" class="code">function externa(x) {</text>
<text x="65" y="363" class="code">  // Variable en scope externo</text>
<text x="65" y="381" class="code">  </text>
<text x="65" y="399" class="code">  function interna(y) {</text>
<text x="65" y="417" class="code">    return x + y; // Accede a x del scope externo</text>
<text x="65" y="435" class="code">  }</text>
<text x="65" y="453" class="code">  </text>
<text x="65" y="471" class="code">  return interna;</text>
<text x="65" y="489" class="code">}</text>
<text x="65" y="507" class="code"></text>
<text x="65" y="525" class="code">const miFuncion = externa(10);</text>
<text x="65" y="543" class="code">console.log(miFuncion(5)); // 15</text>

        </g>
        
        <!-- Caso de uso: Módulo -->
        
        <g class="code-group">
            <rect x="620" y="320" width="530" height="150" class="code-box" rx="4" />
            <text x="635" y="345" class="code">function crearContador() {</text>
<text x="635" y="363" class="code">  let count = 0; // Variable privada</text>
<text x="635" y="381" class="code">  </text>
<text x="635" y="399" class="code">  return {</text>
<text x="635" y="417" class="code">    incrementar: () =&gt; ++count,</text>
<text x="635" y="435" class="code">    decrementar: () =&gt; --count,</text>
<text x="635" y="453" class="code">    obtener: () =&gt; count</text>
<text x="635" y="471" class="code">  };</text>
<text x="635" y="489" class="code">}</text>
<text x="635" y="507" class="code"></text>
<text x="635" y="525" class="code">const contador = crearContador();</text>
<text x="635" y="543" class="code">console.log(contador.obtener()); // 0</text>

        </g>
        
        <!-- Problemas comunes -->
        
        <g class="box-group">
            <rect x="50" y="490" width="530" height="100" class="warning-box" rx="8" />
            <text x="65" y="515" class="subtitle">Problemas comunes</text>
            <text x="65" y="540" class="text">Memory leaks: Los closures mantienen referencias
Bucles con closures: Usar let en lugar de var
Rendimiento: Evitar closures innecesarios en funciones frecuentes</text>
        </g>
        
        <!-- Casos de uso prácticos -->
        
        <g class="box-group">
            <rect x="620" y="490" width="530" height="100" class="example-box" rx="8" />
            <text x="635" y="515" class="subtitle">Casos de uso prácticos</text>
            <text x="635" y="540" class="text">Módulos y encapsulación
Callbacks y event handlers
Funciones de orden superior
Factory functions
Currying y partial application</text>
        </g>
        
        <!-- Debugging -->
        
        <g class="box-group">
            <rect x="50" y="610" width="1100" height="80" class="example-box" rx="8" />
            <text x="65" y="635" class="subtitle">Debugging y troubleshooting</text>
            <text x="65" y="660" class="text">Usar debugger para inspeccionar scope. Verificar referencias con DevTools. Cuidado con variables que cambian en bucles. Liberar referencias para evitar memory leaks.</text>
        </g>
        
    </g>
</svg>