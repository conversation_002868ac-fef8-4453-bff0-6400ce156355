
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Variables y tipos de datos</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
        <!-- Definición -->
        
        <g class="box-group">
            <rect x="50" y="80" width="700" height="100" class="concept-box" rx="8" />
            <text x="65" y="105" class="subtitle">Definición</text>
            <text x="65" y="130" class="text">Las variables son contenedores para almacenar datos. En JavaScript existen tres formas de declarar variables: var, let y const.</text>
        </g>
        
        <!-- Sintaxis -->
        
        <g class="box-group">
            <rect x="50" y="200" width="320" height="150" class="concept-box" rx="8" />
            <text x="65" y="225" class="subtitle">Sintaxis</text>
            <text x="65" y="250" class="text">var nombre = valor; // Ámbito de función, puede redeclararse
let nombre = valor; // Ámbito de bloque, no puede redeclararse
const nombre = valor; // Ámbito de bloque, no puede modificarse</text>
        </g>
        
        <!-- Tipos de datos -->
        
        <g class="box-group">
            <rect x="430" y="200" width="320" height="150" class="concept-box" rx="8" />
            <text x="445" y="225" class="subtitle">Tipos de datos</text>
            <text x="445" y="250" class="text">String: "Hola"
Number: 42, 3.14
Boolean: true, false
Undefined: undefined
Null: null
Object: {nombre: "valor"}
Array: [1, 2, 3]</text>
        </g>
        
        <!-- Ejemplo práctico -->
        
        <g class="code-group">
            <rect x="50" y="380" width="700" height="120" class="code-box" rx="4" />
            <text x="65" y="405" class="code">let nombre = &quot;Ana&quot;;</text>
<text x="65" y="423" class="code">const edad = 25;</text>
<text x="65" y="441" class="code">let esEstudiante = true;</text>
<text x="65" y="459" class="code"></text>
<text x="65" y="477" class="code">console.log(nombre); // &quot;Ana&quot;</text>
<text x="65" y="495" class="code">console.log(typeof edad); // &quot;number&quot;</text>

        </g>
        
        <!-- Errores comunes -->
        
        <g class="box-group">
            <rect x="50" y="520" width="700" height="60" class="warning-box" rx="8" />
            <text x="65" y="545" class="subtitle">Errores comunes</text>
            <text x="65" y="570" class="text">Usar const para valores que necesitan cambiar. Usar var en lugar de let. No inicializar variables.</text>
        </g>
        
    </g>
</svg>