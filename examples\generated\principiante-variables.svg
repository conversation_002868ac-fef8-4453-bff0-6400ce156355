
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Variables y tipos de datos</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
        <g class="box-group">
            <rect x="50" y="80" width="400" height="142" class="concept-box" rx="8" />
            <text x="65" y="105" class="subtitle">Definición</text>
            <text x="65" y="130" class="text">Las variables son contenedores para</text>
<text x="65" y="148" class="text">almacenar datos. En JavaScript existen tres</text>
<text x="65" y="166" class="text">formas de declarar variables: var, let y</text>
<text x="65" y="184" class="text">const.</text>

        </g>
        <g class="box-group">
            <rect x="470" y="80" width="400" height="124" class="concept-box" rx="8" />
            <text x="485" y="105" class="subtitle">Declaración con var</text>
            <text x="485" y="130" class="text">var nombre = valor; Tiene ámbito de función,</text>
<text x="485" y="148" class="text">puede redeclararse y reasignarse. Se eleva</text>
<text x="485" y="166" class="text">(hoisting) al inicio de la función.</text>

        </g>
        <g class="box-group">
            <rect x="50" y="242" width="400" height="124" class="concept-box" rx="8" />
            <text x="65" y="267" class="subtitle">Declaración con let</text>
            <text x="65" y="292" class="text">let nombre = valor; Tiene ámbito de bloque,</text>
<text x="65" y="310" class="text">no puede redeclararse pero sí reasignarse.</text>
<text x="65" y="328" class="text">Más seguro que var.</text>

        </g>
        <g class="box-group">
            <rect x="470" y="242" width="400" height="142" class="concept-box" rx="8" />
            <text x="485" y="267" class="subtitle">Declaración con const</text>
            <text x="485" y="292" class="text">const nombre = valor; Tiene ámbito de</text>
<text x="485" y="310" class="text">bloque, no puede redeclararse ni</text>
<text x="485" y="328" class="text">reasignarse. Debe inicializarse al</text>
<text x="485" y="346" class="text">declararla.</text>

        </g>
        <g class="box-group">
            <rect x="50" y="404" width="400" height="124" class="concept-box" rx="8" />
            <text x="65" y="429" class="subtitle">Tipos de datos primitivos</text>
            <text x="65" y="454" class="text">String: &quot;Hola mundo&quot; - Number: 42, 3.14 -</text>
<text x="65" y="472" class="text">Boolean: true, false - Undefined: undefined</text>
<text x="65" y="490" class="text">- Null: null - Symbol: Symbol(&quot;id&quot;)</text>

        </g>
        <g class="box-group">
            <rect x="470" y="404" width="400" height="124" class="concept-box" rx="8" />
            <text x="485" y="429" class="subtitle">Tipos de datos complejos</text>
            <text x="485" y="454" class="text">Object: {nombre: &quot;Ana&quot;, edad: 25} - Array:</text>
<text x="485" y="472" class="text">[1, 2, 3, &quot;texto&quot;] - Function: function() {}</text>
<text x="485" y="490" class="text">- Date: new Date()</text>

        </g>
        <g class="code-group">
            <rect x="50" y="548" width="450" height="160" class="code-box" rx="4" />
            <text x="65" y="573" class="code">let nombre = &quot;Ana&quot;;</text>
<text x="65" y="589" class="code">const edad = 25;</text>
<text x="65" y="605" class="code">let esEstudiante = true;</text>
<text x="65" y="621" class="code">let hobbies = [&quot;leer&quot;, &quot;programar&quot;];</text>
<text x="65" y="637" class="code"></text>
<text x="65" y="653" class="code">console.log(nombre); // &quot;Ana&quot;</text>
<text x="65" y="669" class="code">console.log(typeof edad); // &quot;number&quot;</text>
<text x="65" y="685" class="code">console.log(esEstudiante); // true</text>

        </g>
        <g class="box-group">
            <rect x="520" y="548" width="400" height="142" class="warning-box" rx="8" />
            <text x="535" y="573" class="subtitle">Errores comunes</text>
            <text x="535" y="598" class="text">Usar const para valores que necesitan</text>
<text x="535" y="616" class="text">cambiar. Usar var en lugar de let. No</text>
<text x="535" y="634" class="text">inicializar variables. Confundir null con</text>
<text x="535" y="652" class="text">undefined.</text>

        </g>
        <g class="box-group">
            <rect x="50" y="728" width="400" height="142" class="example-box" rx="8" />
            <text x="65" y="753" class="subtitle">Buenas prácticas</text>
            <text x="65" y="778" class="text">Usar const por defecto. Usar let cuando</text>
<text x="65" y="796" class="text">necesites reasignar. Evitar var. Usar</text>
<text x="65" y="814" class="text">nombres descriptivos. Inicializar variables</text>
<text x="65" y="832" class="text">al declararlas.</text>

        </g>
    </g>
</svg>