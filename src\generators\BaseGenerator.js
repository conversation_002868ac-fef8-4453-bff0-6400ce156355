const AdvancedVisualElements = require('./AdvancedVisualElements');

class BaseGenerator {
    constructor() {
        this.topics = {};
        this.examples = {};
        this.visualElements = new AdvancedVisualElements();
        this.colors = {
            primary: '#2563eb',      // Azul para conceptos
            secondary: '#16a34a',    // Verde para ejemplos
            warning: '#ea580c',      // Naranja para advertencias
            background: '#f8fafc',   // Fondo claro
            text: '#1e293b',         // Texto principal
            code: '#374151',         // Código
            border: '#e2e8f0'        // Bordes
        };
    }

    /**
     * Genera el SVG base con estructura común
     * @param {string} title - Título del diagrama
     * @param {object} options - Opciones de configuración
     * @returns {string} - SVG base
     */
    generateBaseSVG(title, options = {}) {
        const { width = 800, height = 600 } = options;
        
        return `
<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: ${this.colors.text}; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: ${this.colors.text}; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: ${this.colors.text}; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: ${this.colors.code}; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: ${this.colors.text}; }
            .concept-box { fill: ${this.colors.primary}; fill-opacity: 0.1; stroke: ${this.colors.primary}; stroke-width: 2; }
            .example-box { fill: ${this.colors.secondary}; fill-opacity: 0.1; stroke: ${this.colors.secondary}; stroke-width: 2; }
            .warning-box { fill: ${this.colors.warning}; fill-opacity: 0.1; stroke: ${this.colors.warning}; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: ${this.colors.border}; stroke-width: 1; }
            .arrow { stroke: ${this.colors.text}; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="${this.colors.text}" />
        </marker>
        <marker id="arrowhead-normal" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
        </marker>
        <marker id="arrowhead-true" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
        </marker>
        <marker id="arrowhead-false" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#ea580c" />
        </marker>
        <marker id="arrowhead-process" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#2563eb" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="${this.colors.background}" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">${title}</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        <!-- El contenido específico se añadirá aquí -->
    </g>
</svg>`;
    }

    /**
     * Crea una caja conceptual con texto que se ajusta automáticamente
     * @param {number} x - Posición X
     * @param {number} y - Posición Y
     * @param {number} width - Ancho
     * @param {number} height - Alto
     * @param {string} title - Título de la caja
     * @param {string} content - Contenido de la caja
     * @param {string} type - Tipo de caja (concept, example, warning)
     * @returns {string} - SVG de la caja
     */
    createBox(x, y, width, height, title, content, type = 'concept') {
        const boxClass = `${type}-box`;
        const titleY = y + 25;
        const contentStartY = y + 50;
        const padding = 15;
        const maxWidth = width - (padding * 2);

        // Dividir el contenido en líneas que caben en el ancho disponible
        const wrappedContent = this.wrapText(content, maxWidth, 14);

        let contentElements = '';
        wrappedContent.forEach((line, index) => {
            const lineY = contentStartY + (index * 18);
            contentElements += `<text x="${x + padding}" y="${lineY}" class="text">${this.escapeXML(line)}</text>\n`;
        });

        return `
        <g class="box-group">
            <rect x="${x}" y="${y}" width="${width}" height="${height}" class="${boxClass}" rx="8" />
            <text x="${x + padding}" y="${titleY}" class="subtitle">${this.escapeXML(title)}</text>
            ${contentElements}
        </g>`;
    }

    /**
     * Crea una caja de código con ajuste automático
     * @param {number} x - Posición X
     * @param {number} y - Posición Y
     * @param {number} width - Ancho
     * @param {number} height - Alto
     * @param {string} code - Código a mostrar
     * @returns {string} - SVG de la caja de código
     */
    createCodeBox(x, y, width, height, code) {
        const lines = code.split('\n');
        const padding = 15;
        const maxWidth = width - (padding * 2);
        const fontSize = 12;
        let codeElements = '';
        let currentY = y + 25;

        lines.forEach((line) => {
            // Para código, preservamos la estructura pero ajustamos líneas muy largas
            if (line.length * fontSize * 0.5 > maxWidth) {
                // Línea muy larga, dividir en puntos lógicos
                const wrappedLines = this.wrapCodeLine(line, maxWidth, fontSize);
                wrappedLines.forEach((wrappedLine, wrapIndex) => {
                    const indent = wrapIndex > 0 ? '  ' : '';
                    codeElements += `<text x="${x + padding}" y="${currentY}" class="code">${this.escapeXML(indent + wrappedLine)}</text>\n`;
                    currentY += 16;
                });
            } else {
                codeElements += `<text x="${x + padding}" y="${currentY}" class="code">${this.escapeXML(line)}</text>\n`;
                currentY += 16;
            }
        });

        return `
        <g class="code-group">
            <rect x="${x}" y="${y}" width="${width}" height="${height}" class="code-box" rx="4" />
            ${codeElements}
        </g>`;
    }

    /**
     * Ajusta una línea de código respetando la sintaxis
     * @param {string} line - Línea de código
     * @param {number} maxWidth - Ancho máximo
     * @param {number} fontSize - Tamaño de fuente
     * @returns {Array} - Líneas ajustadas
     */
    wrapCodeLine(line, maxWidth, fontSize = 12) {
        const charWidth = fontSize * 0.5; // Fuente monospace es más estrecha
        const maxCharsPerLine = Math.floor(maxWidth / charWidth);

        if (line.length <= maxCharsPerLine) {
            return [line];
        }

        // Buscar puntos de ruptura lógicos (espacios, operadores, etc.)
        const breakPoints = [' ', '.', '(', ')', '{', '}', '[', ']', ',', ';'];
        const lines = [];
        let currentLine = '';
        let i = 0;

        while (i < line.length) {
            if (currentLine.length >= maxCharsPerLine - 10) {
                // Buscar punto de ruptura hacia atrás
                let breakIndex = -1;
                for (let j = currentLine.length - 1; j >= Math.max(0, currentLine.length - 20); j--) {
                    if (breakPoints.includes(currentLine[j])) {
                        breakIndex = j + 1;
                        break;
                    }
                }

                if (breakIndex > 0) {
                    lines.push(currentLine.substring(0, breakIndex));
                    currentLine = currentLine.substring(breakIndex);
                } else {
                    lines.push(currentLine);
                    currentLine = '';
                }
            } else {
                currentLine += line[i];
                i++;
            }
        }

        if (currentLine) {
            lines.push(currentLine);
        }

        return lines;
    }

    /**
     * Crea una flecha
     * @param {number} x1 - Posición X inicial
     * @param {number} y1 - Posición Y inicial
     * @param {number} x2 - Posición X final
     * @param {number} y2 - Posición Y final
     * @param {string} label - Etiqueta opcional
     * @returns {string} - SVG de la flecha
     */
    createArrow(x1, y1, x2, y2, label = '') {
        let arrow = `<line x1="${x1}" y1="${y1}" x2="${x2}" y2="${y2}" class="arrow" />`;
        
        if (label) {
            const midX = (x1 + x2) / 2;
            const midY = (y1 + y2) / 2 - 10;
            arrow += `<text x="${midX}" y="${midY}" text-anchor="middle" class="small-text">${label}</text>`;
        }
        
        return arrow;
    }

    /**
     * Escapa caracteres XML especiales
     * @param {string} text - Texto a escapar
     * @returns {string} - Texto escapado
     */
    escapeXML(text) {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }

    /**
     * Ajusta texto para que quepa en un ancho específico
     * @param {string} text - Texto a ajustar
     * @param {number} maxWidth - Ancho máximo en píxeles
     * @param {number} fontSize - Tamaño de fuente
     * @returns {Array} - Array de líneas ajustadas
     */
    wrapText(text, maxWidth, fontSize = 14) {
        // Estimación aproximada: cada carácter ocupa ~0.6 del tamaño de fuente
        const charWidth = fontSize * 0.6;
        const maxCharsPerLine = Math.floor(maxWidth / charWidth);

        const words = text.split(' ');
        const lines = [];
        let currentLine = '';

        words.forEach(word => {
            const testLine = currentLine ? `${currentLine} ${word}` : word;

            if (testLine.length <= maxCharsPerLine) {
                currentLine = testLine;
            } else {
                if (currentLine) {
                    lines.push(currentLine);
                    currentLine = word;
                } else {
                    // Palabra muy larga, dividirla
                    if (word.length > maxCharsPerLine) {
                        for (let i = 0; i < word.length; i += maxCharsPerLine) {
                            lines.push(word.substring(i, i + maxCharsPerLine));
                        }
                        currentLine = '';
                    } else {
                        currentLine = word;
                    }
                }
            }
        });

        if (currentLine) {
            lines.push(currentLine);
        }

        return lines;
    }

    /**
     * Calcula la altura necesaria para un texto ajustado
     * @param {string} text - Texto
     * @param {number} maxWidth - Ancho máximo
     * @param {number} fontSize - Tamaño de fuente
     * @param {number} lineHeight - Altura de línea
     * @returns {number} - Altura necesaria
     */
    calculateTextHeight(text, maxWidth, fontSize = 14, lineHeight = 18) {
        const lines = this.wrapText(text, maxWidth, fontSize);
        return lines.length * lineHeight;
    }

    /**
     * Verifica si un tema está soportado
     * @param {string} topic - Tema a verificar
     * @returns {boolean} - True si está soportado
     */
    hasTopicSupport(topic) {
        return Object.keys(this.topics).includes(topic.toLowerCase());
    }

    /**
     * Obtiene los temas disponibles
     * @returns {array} - Lista de temas
     */
    getAvailableTopics() {
        return Object.keys(this.topics);
    }

    /**
     * Método abstracto para generar diagramas
     * @param {string} topic - Tema a generar
     * @param {object} options - Opciones
     * @returns {string} - SVG generado
     */
    generate(topic, options = {}) {
        throw new Error('El método generate debe ser implementado por las clases hijas');
    }

    /**
     * Obtiene un ejemplo para un tema
     * @param {string} topic - Tema
     * @returns {object} - Ejemplo
     */
    getExample(topic) {
        const normalizedTopic = topic.toLowerCase();
        if (!this.examples[normalizedTopic]) {
            throw new Error(`No hay ejemplo disponible para el tema: ${topic}`);
        }
        return this.examples[normalizedTopic];
    }

    /**
     * Crea un layout automático para elementos del diagrama
     * @param {Array} elements - Array de elementos {type, title, content, width?, height?}
     * @param {Object} options - Opciones de layout
     * @returns {Object} - Layout calculado con posiciones
     */
    createAutoLayout(elements, options = {}) {
        const {
            startX = 50,
            startY = 80,
            padding = 20,
            maxWidth = 1100,
            columnsPreferred = 2
        } = options;

        const layout = {
            elements: [],
            totalWidth: 0,
            totalHeight: 0
        };

        let currentX = startX;
        let currentY = startY;
        let rowHeight = 0;
        let elementsInRow = 0;

        elements.forEach((element, index) => {
            // Calcular dimensiones del elemento
            const elementWidth = element.width || this.calculateElementWidth(element);
            const elementHeight = element.height || this.calculateElementHeight(element);

            // Verificar si necesitamos nueva fila
            if (elementsInRow >= columnsPreferred || currentX + elementWidth > maxWidth) {
                currentX = startX;
                currentY += rowHeight + padding;
                rowHeight = 0;
                elementsInRow = 0;
            }

            // Añadir elemento al layout
            layout.elements.push({
                ...element,
                x: currentX,
                y: currentY,
                width: elementWidth,
                height: elementHeight
            });

            // Actualizar posición para siguiente elemento
            currentX += elementWidth + padding;
            rowHeight = Math.max(rowHeight, elementHeight);
            elementsInRow++;

            // Actualizar dimensiones totales
            layout.totalWidth = Math.max(layout.totalWidth, currentX - padding);
            layout.totalHeight = Math.max(layout.totalHeight, currentY + elementHeight);
        });

        return layout;
    }

    /**
     * Calcula el ancho necesario para un elemento
     * @param {Object} element - Elemento
     * @returns {number} - Ancho calculado
     */
    calculateElementWidth(element) {
        const baseWidth = 350;
        const titleLength = (element.title || '').length;
        const contentLength = (element.content || '').length;

        // Ajustar ancho basado en contenido
        if (contentLength > 200) return Math.min(500, baseWidth + 100);
        if (contentLength > 100) return Math.min(450, baseWidth + 50);
        if (titleLength > 30) return Math.min(400, baseWidth + 25);

        return baseWidth;
    }

    /**
     * Calcula la altura necesaria para un elemento
     * @param {Object} element - Elemento
     * @returns {number} - Altura calculada
     */
    calculateElementHeight(element) {
        const baseHeight = 120;
        const titleHeight = 30;
        const contentHeight = this.calculateTextHeight(
            element.content || '',
            this.calculateElementWidth(element) - 30
        );

        return Math.max(baseHeight, titleHeight + contentHeight + 40);
    }
}

module.exports = BaseGenerator;
