
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Objetos y propiedades</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
        <g class="box-group">
            <rect x="50" y="80" width="400" height="142" class="concept-box" rx="8" />
            <text x="65" y="105" class="subtitle">Definición</text>
            <text x="65" y="130" class="text">Los objetos son colecciones de propiedades</text>
<text x="65" y="148" class="text">(pares clave-valor). Son la base de</text>
<text x="65" y="166" class="text">JavaScript y permiten agrupar datos</text>
<text x="65" y="184" class="text">relacionados.</text>

        </g>
        <g class="box-group">
            <rect x="470" y="80" width="400" height="124" class="concept-box" rx="8" />
            <text x="485" y="105" class="subtitle">Creación de objetos</text>
            <text x="485" y="130" class="text">Literal: {nombre: &quot;Ana&quot;, edad: 25} -</text>
<text x="485" y="148" class="text">Constructor: new Object() - Object.create()</text>
<text x="485" y="166" class="text">- Clases: new MiClase()</text>

        </g>
        <g class="box-group">
            <rect x="50" y="242" width="400" height="124" class="concept-box" rx="8" />
            <text x="65" y="267" class="subtitle">Acceso a propiedades</text>
            <text x="65" y="292" class="text">Notación punto: objeto.propiedad - Notación</text>
<text x="65" y="310" class="text">corchetes: objeto[&quot;propiedad&quot;] - Variables:</text>
<text x="65" y="328" class="text">objeto[variable]</text>

        </g>
        <g class="box-group">
            <rect x="470" y="242" width="400" height="124" class="concept-box" rx="8" />
            <text x="485" y="267" class="subtitle">Modificar propiedades</text>
            <text x="485" y="292" class="text">Añadir: objeto.nuevaPropiedad = valor -</text>
<text x="485" y="310" class="text">Modificar: objeto.propiedad = nuevoValor -</text>
<text x="485" y="328" class="text">Eliminar: delete objeto.propiedad</text>

        </g>
        <g class="box-group">
            <rect x="50" y="386" width="400" height="142" class="concept-box" rx="8" />
            <text x="65" y="411" class="subtitle">Métodos de objeto</text>
            <text x="65" y="436" class="text">Object.keys(): Obtener claves -</text>
<text x="65" y="454" class="text">Object.values(): Obtener valores -</text>
<text x="65" y="472" class="text">Object.entries(): Obtener pares clave-valor</text>
<text x="65" y="490" class="text">- hasOwnProperty(): Verificar propiedad</text>

        </g>
        <g class="code-group">
            <rect x="470" y="386" width="450" height="178" class="code-box" rx="4" />
            <text x="485" y="411" class="code">let persona = {</text>
<text x="485" y="427" class="code">  nombre: &quot;Ana&quot;,</text>
<text x="485" y="443" class="code">  edad: 25,</text>
<text x="485" y="459" class="code">  saludar: function() {</text>
<text x="485" y="475" class="code">    return &quot;Hola, soy &quot; + this.nombre;</text>
<text x="485" y="491" class="code">  }</text>
<text x="485" y="507" class="code">};</text>
<text x="485" y="523" class="code"></text>
<text x="485" y="539" class="code">console.log(persona.nombre); // &quot;Ana&quot;</text>
<text x="485" y="555" class="code">console.log(persona.saludar()); // &quot;Hola, soy Ana&quot;</text>
<text x="485" y="571" class="code">persona.apellido = &quot;García&quot;; // Añadir propiedad</text>

        </g>
    </g>
</svg>