class AdvancedVisualElements {
    constructor() {
        this.colors = {
            primary: '#2563eb',
            secondary: '#16a34a',
            warning: '#ea580c',
            success: '#10b981',
            info: '#0ea5e9',
            purple: '#8b5cf6',
            pink: '#ec4899',
            gray: '#6b7280',
            background: '#f8fafc',
            text: '#1e293b',
            border: '#e2e8f0'
        };
    }

    /**
     * Crea un diagrama de flujo con nodos y conexiones
     * @param {Array} nodes - Array de nodos {id, type, label, x, y}
     * @param {Array} connections - Array de conexiones {from, to, label?, type?}
     * @param {Object} options - Opciones del diagrama
     * @returns {string} - SVG del diagrama de flujo
     */
    createFlowChart(nodes, connections, options = {}) {
        const { width = 800, height = 600, startX = 50, startY = 50 } = options;
        
        let svg = '';
        
        // Crear conexiones primero (para que estén detrás de los nodos)
        connections.forEach(conn => {
            const fromNode = nodes.find(n => n.id === conn.from);
            const toNode = nodes.find(n => n.id === conn.to);
            
            if (fromNode && toNode) {
                svg += this.createConnection(fromNode, toNode, conn.label, conn.type);
            }
        });
        
        // Crear nodos
        nodes.forEach(node => {
            svg += this.createFlowNode(node.x, node.y, node.label, node.type, node.id);
        });
        
        return svg;
    }

    /**
     * Crea un nodo de diagrama de flujo
     * @param {number} x - Posición X
     * @param {number} y - Posición Y
     * @param {string} label - Etiqueta del nodo
     * @param {string} type - Tipo de nodo (start, process, decision, end)
     * @param {string} id - ID del nodo
     * @returns {string} - SVG del nodo
     */
    createFlowNode(x, y, label, type = 'process', id = '') {
        const width = 120;
        const height = 60;
        let shape = '';
        let color = this.colors.primary;
        
        switch (type) {
            case 'start':
            case 'end':
                // Óvalo para inicio/fin
                shape = `<ellipse cx="${x + width/2}" cy="${y + height/2}" rx="${width/2}" ry="${height/2}" 
                         fill="${color}" fill-opacity="0.1" stroke="${color}" stroke-width="2"/>`;
                color = type === 'start' ? this.colors.success : this.colors.warning;
                break;
            case 'decision':
                // Rombo para decisiones
                const centerX = x + width/2;
                const centerY = y + height/2;
                shape = `<polygon points="${centerX},${y} ${x + width},${centerY} ${centerX},${y + height} ${x},${centerY}" 
                         fill="${this.colors.info}" fill-opacity="0.1" stroke="${this.colors.info}" stroke-width="2"/>`;
                color = this.colors.info;
                break;
            case 'process':
            default:
                // Rectángulo para procesos
                shape = `<rect x="${x}" y="${y}" width="${width}" height="${height}" rx="8" 
                         fill="${color}" fill-opacity="0.1" stroke="${color}" stroke-width="2"/>`;
                break;
        }
        
        // Ajustar texto para que quepa en el nodo
        const lines = this.wrapTextForNode(label, width - 20);
        let textElements = '';
        const textY = y + height/2 - (lines.length - 1) * 6;
        
        lines.forEach((line, index) => {
            textElements += `<text x="${x + width/2}" y="${textY + index * 12}" 
                            text-anchor="middle" class="small-text" fill="${color}">${line}</text>\n`;
        });
        
        return `
        <g class="flow-node" data-id="${id}">
            ${shape}
            ${textElements}
        </g>`;
    }

    /**
     * Crea una conexión entre dos nodos
     * @param {Object} fromNode - Nodo origen
     * @param {Object} toNode - Nodo destino
     * @param {string} label - Etiqueta de la conexión
     * @param {string} type - Tipo de conexión
     * @returns {string} - SVG de la conexión
     */
    createConnection(fromNode, toNode, label = '', type = 'normal') {
        const fromX = fromNode.x + 60; // Centro del nodo
        const fromY = fromNode.y + 30;
        const toX = toNode.x + 60;
        const toY = toNode.y + 30;
        
        // Calcular puntos de conexión en los bordes de los nodos
        const { startX, startY, endX, endY } = this.calculateConnectionPoints(fromNode, toNode);
        
        let strokeColor = this.colors.gray;
        let strokeWidth = 2;
        
        if (type === 'true') {
            strokeColor = this.colors.success;
        } else if (type === 'false') {
            strokeColor = this.colors.warning;
        }
        
        let path = '';
        
        // Crear línea curva si los nodos están en diferentes niveles
        if (Math.abs(startY - endY) > 100) {
            const midY = (startY + endY) / 2;
            path = `M ${startX} ${startY} Q ${startX} ${midY} ${endX} ${endY}`;
        } else {
            path = `M ${startX} ${startY} L ${endX} ${endY}`;
        }
        
        let labelElement = '';
        if (label) {
            const midX = (startX + endX) / 2;
            const midY = (startY + endY) / 2;
            labelElement = `<text x="${midX}" y="${midY - 5}" text-anchor="middle" 
                           class="small-text" fill="${strokeColor}">${label}</text>`;
        }
        
        return `
        <g class="connection">
            <path d="${path}" stroke="${strokeColor}" stroke-width="${strokeWidth}" 
                  fill="none" marker-end="url(#arrowhead-${type})"/>
            ${labelElement}
        </g>`;
    }

    /**
     * Crea un árbol de sintaxis abstracta (AST)
     * @param {Object} astNode - Nodo raíz del AST
     * @param {Object} options - Opciones del árbol
     * @returns {string} - SVG del árbol
     */
    createSyntaxTree(astNode, options = {}) {
        const { startX = 400, startY = 50, levelHeight = 80, nodeWidth = 100 } = options;
        
        let svg = '';
        let nodeId = 0;
        
        const drawNode = (node, x, y, level = 0) => {
            const currentId = nodeId++;
            const nodeColor = this.getNodeColorByType(node.type);
            
            // Dibujar nodo
            svg += `
            <g class="ast-node" data-level="${level}">
                <rect x="${x - nodeWidth/2}" y="${y}" width="${nodeWidth}" height="40" rx="6"
                      fill="${nodeColor}" fill-opacity="0.2" stroke="${nodeColor}" stroke-width="2"/>
                <text x="${x}" y="${y + 25}" text-anchor="middle" class="small-text" fill="${nodeColor}">
                    ${node.type}
                </text>
            </g>`;
            
            // Dibujar valor si existe
            if (node.value !== undefined) {
                svg += `<text x="${x}" y="${y + 55}" text-anchor="middle" class="small-text" 
                       fill="${this.colors.gray}">${node.value}</text>`;
            }
            
            // Dibujar hijos
            if (node.children && node.children.length > 0) {
                const childrenWidth = node.children.length * (nodeWidth + 20);
                const startChildX = x - childrenWidth / 2 + nodeWidth / 2;
                
                node.children.forEach((child, index) => {
                    const childX = startChildX + index * (nodeWidth + 20);
                    const childY = y + levelHeight;
                    
                    // Línea de conexión
                    svg += `<line x1="${x}" y1="${y + 40}" x2="${childX}" y2="${childY}" 
                           stroke="${this.colors.gray}" stroke-width="1"/>`;
                    
                    // Dibujar hijo recursivamente
                    drawNode(child, childX, childY, level + 1);
                });
            }
        };
        
        drawNode(astNode, startX, startY);
        return svg;
    }

    /**
     * Crea un diagrama de análisis de scope
     * @param {Array} scopes - Array de scopes {name, variables, parent?, level}
     * @param {Object} options - Opciones del diagrama
     * @returns {string} - SVG del diagrama de scope
     */
    createScopeAnalysis(scopes, options = {}) {
        const { startX = 50, startY = 50, scopeWidth = 200, scopeHeight = 150 } = options;
        
        let svg = '';
        let currentY = startY;
        
        scopes.forEach((scope, index) => {
            const x = startX + (scope.level || 0) * 50;
            const y = currentY;
            
            // Caja del scope
            const scopeColor = this.getScopeColor(scope.level || 0);
            svg += `
            <g class="scope-container">
                <rect x="${x}" y="${y}" width="${scopeWidth}" height="${scopeHeight}" rx="8"
                      fill="${scopeColor}" fill-opacity="0.1" stroke="${scopeColor}" stroke-width="2"/>
                <text x="${x + 10}" y="${y + 25}" class="subtitle" fill="${scopeColor}">
                    ${scope.name} Scope
                </text>
            </g>`;
            
            // Variables en el scope
            if (scope.variables && scope.variables.length > 0) {
                scope.variables.forEach((variable, varIndex) => {
                    const varY = y + 45 + varIndex * 20;
                    svg += `<text x="${x + 15}" y="${varY}" class="small-text" fill="${this.colors.text}">
                            ${variable.name}: ${variable.type || 'any'}
                            </text>`;
                });
            }
            
            // Flecha al scope padre si existe
            if (scope.parent !== undefined && scope.parent < index) {
                const parentScope = scopes[scope.parent];
                if (parentScope) {
                    const parentX = startX + (parentScope.level || 0) * 50;
                    const parentY = startY + scope.parent * (scopeHeight + 30);
                    
                    svg += this.createScopeConnection(x, y, parentX + scopeWidth, parentY + scopeHeight/2);
                }
            }
            
            currentY += scopeHeight + 30;
        });
        
        return svg;
    }

    /**
     * Crea un grafo de llamadas de funciones
     * @param {Array} functions - Array de funciones {name, calls, x?, y?}
     * @param {Object} options - Opciones del grafo
     * @returns {string} - SVG del grafo de llamadas
     */
    createCallGraph(functions, options = {}) {
        const { width = 800, height = 600 } = options;
        
        // Calcular posiciones automáticamente si no se proporcionan
        functions = this.calculateCallGraphPositions(functions, width, height);
        
        let svg = '';
        
        // Dibujar conexiones primero
        functions.forEach(func => {
            if (func.calls) {
                func.calls.forEach(calledFunc => {
                    const target = functions.find(f => f.name === calledFunc);
                    if (target) {
                        svg += this.createFunctionCall(func, target);
                    }
                });
            }
        });
        
        // Dibujar nodos de funciones
        functions.forEach(func => {
            svg += this.createFunctionNode(func.x, func.y, func.name, func.type || 'function');
        });
        
        return svg;
    }

    // Métodos auxiliares
    wrapTextForNode(text, maxWidth) {
        const maxChars = Math.floor(maxWidth / 8); // Aproximación
        if (text.length <= maxChars) return [text];
        
        const words = text.split(' ');
        const lines = [];
        let currentLine = '';
        
        words.forEach(word => {
            if ((currentLine + word).length <= maxChars) {
                currentLine += (currentLine ? ' ' : '') + word;
            } else {
                if (currentLine) lines.push(currentLine);
                currentLine = word;
            }
        });
        
        if (currentLine) lines.push(currentLine);
        return lines;
    }

    calculateConnectionPoints(fromNode, toNode) {
        const fromCenterX = fromNode.x + 60;
        const fromCenterY = fromNode.y + 30;
        const toCenterX = toNode.x + 60;
        const toCenterY = toNode.y + 30;
        
        // Simplificado: usar centros de nodos
        return {
            startX: fromCenterX,
            startY: fromCenterY + 30, // Salir por abajo
            endX: toCenterX,
            endY: toCenterY - 30 // Entrar por arriba
        };
    }

    getNodeColorByType(type) {
        const colorMap = {
            'Program': this.colors.primary,
            'FunctionDeclaration': this.colors.success,
            'VariableDeclaration': this.colors.info,
            'BinaryExpression': this.colors.warning,
            'Identifier': this.colors.purple,
            'Literal': this.colors.pink
        };
        return colorMap[type] || this.colors.gray;
    }

    getScopeColor(level) {
        const colors = [this.colors.primary, this.colors.success, this.colors.info, this.colors.warning, this.colors.purple];
        return colors[level % colors.length];
    }

    createScopeConnection(fromX, fromY, toX, toY) {
        return `<line x1="${fromX}" y1="${fromY + 75}" x2="${toX}" y2="${toY}" 
                stroke="${this.colors.gray}" stroke-width="1" stroke-dasharray="5,5" 
                marker-end="url(#arrowhead)"/>`;
    }

    calculateCallGraphPositions(functions, width, height) {
        // Distribución circular simple
        const centerX = width / 2;
        const centerY = height / 2;
        const radius = Math.min(width, height) / 3;
        
        return functions.map((func, index) => {
            const angle = (2 * Math.PI * index) / functions.length;
            return {
                ...func,
                x: centerX + radius * Math.cos(angle) - 60,
                y: centerY + radius * Math.sin(angle) - 30
            };
        });
    }

    createFunctionCall(fromFunc, toFunc) {
        const fromX = fromFunc.x + 60;
        const fromY = fromFunc.y + 30;
        const toX = toFunc.x + 60;
        const toY = toFunc.y + 30;
        
        return `<line x1="${fromX}" y1="${fromY}" x2="${toX}" y2="${toY}" 
                stroke="${this.colors.primary}" stroke-width="2" 
                marker-end="url(#arrowhead)"/>`;
    }

    createFunctionNode(x, y, name, type) {
        const width = 120;
        const height = 60;
        const color = type === 'main' ? this.colors.success : this.colors.primary;
        
        return `
        <g class="function-node">
            <rect x="${x}" y="${y}" width="${width}" height="${height}" rx="8"
                  fill="${color}" fill-opacity="0.1" stroke="${color}" stroke-width="2"/>
            <text x="${x + width/2}" y="${y + height/2 + 5}" text-anchor="middle" 
                  class="small-text" fill="${color}">${name}()</text>
        </g>`;
    }
}

module.exports = AdvancedVisualElements;
