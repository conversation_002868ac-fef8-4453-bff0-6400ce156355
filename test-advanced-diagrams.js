const DiagramGenerator = require('./src/generators/DiagramGenerator');
const fs = require('fs');

async function testAdvancedDiagrams() {
    console.log('🧪 Probando diagramas avanzados...\n');

    const generator = new DiagramGenerator();

    // Crear directorio de pruebas
    if (!fs.existsSync('./test-advanced')) {
        fs.mkdirSync('./test-advanced');
    }

    // Probar diagramas de flujo (Intermedio)
    console.log('📊 DIAGRAMAS DE FLUJO (Intermedio):');
    const flowTopics = ['control-flow', 'execution-flow', 'conditional-flow'];
    
    for (const topic of flowTopics) {
        try {
            console.log(`  Generando ${topic}...`);
            const svg = generator.generateDiagram(topic, 'intermedio');
            fs.writeFileSync(`./test-advanced/intermedio-${topic}.svg`, svg, 'utf8');
            console.log(`  ✅ ${topic} generado correctamente`);
        } catch (error) {
            console.log(`  ❌ Error en ${topic}:`, error.message);
        }
    }

    // Probar diagramas de análisis (Avanzado)
    console.log('\n🔍 DIAGRAMAS DE ANÁLISIS (Avanzado):');
    const analysisTopics = ['ast-analysis', 'scope-chain', 'call-graph', 'execution-context'];
    
    for (const topic of analysisTopics) {
        try {
            console.log(`  Generando ${topic}...`);
            const svg = generator.generateDiagram(topic, 'avanzado');
            fs.writeFileSync(`./test-advanced/avanzado-${topic}.svg`, svg, 'utf8');
            console.log(`  ✅ ${topic} generado correctamente`);
        } catch (error) {
            console.log(`  ❌ Error en ${topic}:`, error.message);
        }
    }

    // Probar diagramas expertos
    console.log('\n🚀 DIAGRAMAS EXPERTOS:');
    const expertTopics = ['event-loop-diagram', 'memory-model', 'execution-timeline', 'async-flow'];
    
    for (const topic of expertTopics) {
        try {
            console.log(`  Generando ${topic}...`);
            const svg = generator.generateDiagram(topic, 'experto');
            fs.writeFileSync(`./test-advanced/experto-${topic}.svg`, svg, 'utf8');
            console.log(`  ✅ ${topic} generado correctamente`);
        } catch (error) {
            console.log(`  ❌ Error en ${topic}:`, error.message);
        }
    }

    // Estadísticas finales
    console.log('\n📈 ESTADÍSTICAS FINALES:');
    const levels = generator.getAvailableLevels();
    let totalTopics = 0;
    let advancedTopics = 0;

    levels.forEach(level => {
        console.log(`${level.name}: ${level.topics.length} temas`);
        totalTopics += level.topics.length;
        
        // Contar temas avanzados (con diagramas especiales)
        const advanced = level.topics.filter(topic => 
            topic.includes('flow') || 
            topic.includes('ast') || 
            topic.includes('scope') || 
            topic.includes('graph') || 
            topic.includes('context') || 
            topic.includes('loop') || 
            topic.includes('memory') || 
            topic.includes('timeline') || 
            topic.includes('async')
        );
        advancedTopics += advanced.length;
    });

    console.log(`\n🎯 Total de temas: ${totalTopics}`);
    console.log(`🔬 Temas con diagramas avanzados: ${advancedTopics}`);
    console.log(`📊 Porcentaje de diagramas avanzados: ${Math.round((advancedTopics/totalTopics)*100)}%`);

    console.log('\n🎉 Pruebas de diagramas avanzados completadas!');
    console.log('📂 Archivos generados en: ./test-advanced/');
}

// Ejecutar pruebas
if (require.main === module) {
    testAdvancedDiagrams();
}

module.exports = { testAdvancedDiagrams };
