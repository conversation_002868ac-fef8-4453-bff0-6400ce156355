# Prompt para Generar Diagramas SVG Educativos de JavaScript

## Instrucciones Generales

Genera diagramas SVG educativos que expliquen conceptos de JavaScript de manera visual y detallada. Cada diagrama debe:

### Características Técnicas
- **Formato**: SVG con dimensiones 800x600 píxeles mínimo
- **Colores**: Usar paleta educativa coherente (azules para conceptos, verdes para ejemplos, naranjas para advertencias)
- **Tipografía**: Fuente legible, tamaño 14px para texto principal, 12px para detalles
- **Estructura**: <PERSON><PERSON><PERSON><PERSON> principal, subtí<PERSON><PERSON>, ejemplos de código, flechas direccionales

### Elementos Visuales Requeridos
1. **Bloques de código** con fondo gris claro y borde
2. **Flechas** que muestren flujo de datos/ejecución
3. **Cajas conceptuales** con definiciones claras
4. **Ejemplos prácticos** con resultados esperados
5. **Notas importantes** destacadas visualmente

---

## NIVEL PRINCIPIANTE

### Temas a Cubrir:
- Variables y tipos de datos
- Operadores básicos
- Condicionales (if/else)
- Bucles (for, while)
- Funciones básicas
- Arrays y objetos simples
- Métodos de string básicos

### Formato del Diagrama:
```
[TÍTULO DEL CONCEPTO]
├── Definición clara y simple
├── Sintaxis básica con ejemplo
├── Ejemplo práctico paso a paso
├── Resultado esperado
└── Errores comunes a evitar
```

### Ejemplo de Prompt Específico:
> "Crea un diagrama SVG que explique las **variables en JavaScript** nivel principiante. Incluye: declaración con var/let/const, tipos de datos (string, number, boolean), ejemplos de asignación, y muestra cómo los valores se almacenan en memoria de forma visual."

---

## NIVEL INTERMEDIO

### Temas a Cubrir:
- Scope y hoisting
- Arrow functions
- Destructuring
- Spread operator
- Métodos de array (map, filter, reduce)
- Promesas básicas
- Manipulación del DOM
- Event handling

### Formato del Diagrama:
```
[CONCEPTO INTERMEDIO]
├── Prerequisitos (enlaces a conceptos previos)
├── Explicación detallada con diagramas de flujo
├── Comparación con métodos alternativos
├── Casos de uso comunes
├── Ejemplo completo con múltiples pasos
└── Buenas prácticas
```

### Ejemplo de Prompt Específico:
> "Diseña un diagrama SVG sobre **métodos de array** nivel intermedio. Muestra visualmente cómo map(), filter() y reduce() transforman arrays, incluyendo el flujo de datos, callbacks, y comparaciones lado a lado con ejemplos prácticos."

---

## NIVEL AVANZADO

### Temas a Cubrir:
- Closures y lexical scope
- Prototypes y herencia
- Async/await y manejo de errores
- Módulos ES6
- Patrones de diseño
- Performance optimization
- Testing básico

### Formato del Diagrama:
```
[CONCEPTO AVANZADO]
├── Conceptos fundamentales requeridos
├── Explicación técnica profunda
├── Diagramas de arquitectura/flujo
├── Múltiples enfoques y sus trade-offs
├── Casos de uso en proyectos reales
├── Debugging y troubleshooting
└── Recursos para profundizar
```

### Ejemplo de Prompt Específico:
> "Elabora un diagrama SVG sobre **closures** nivel avanzado. Visualiza el scope chain, cómo se mantienen las referencias, casos de uso prácticos (módulos, callbacks), y problemas comunes como memory leaks."

---

## NIVEL EXPERTO

### Temas a Cubrir:
- Event loop y call stack
- Memory management
- Micro/macro tasks
- Advanced patterns (Observer, Factory, etc.)
- Metaprogramming
- TypeScript integration
- Performance profiling

### Formato del Diagrama:
```
[CONCEPTO EXPERTO]
├── Fundamentos teóricos profundos
├── Implementación interna del motor JS
├── Diagramas de arquitectura compleja
├── Análisis de rendimiento
├── Casos edge y soluciones
├── Integración con ecosistema
└── Tendencias futuras
```

### Ejemplo de Prompt Específico:
> "Construye un diagrama SVG del **Event Loop** nivel experto. Incluye call stack, heap, callback queue, microtask queue, y muestra el flujo completo de ejecución con ejemplos de código asíncrono complejo."

---

## Plantilla de Prompt Completa

**Para usar este prompt, completa los campos entre corchetes:**

```
Genera un diagrama SVG educativo sobre [CONCEPTO] para nivel [PRINCIPIANTE/INTERMEDIO/AVANZADO/EXPERTO] en JavaScript.

Requisitos específicos:
- Dimensiones: 800x600px mínimo
- Incluir: [ELEMENTOS ESPECÍFICOS DEL CONCEPTO]
- Mostrar: [EJEMPLOS ESPECÍFICOS]
- Destacar: [PUNTOS IMPORTANTES]
- Conexiones: [RELACIONES CON OTROS CONCEPTOS]

El diagrama debe ser autocontenido y permitir aprender el concepto sin recursos adicionales.
```

---

## Consejos para Personalizar

### Para Principiantes:
- Usa analogías del mundo real
- Incluye muchos ejemplos simples
- Evita terminología técnica compleja
- Muestra errores comunes y cómo evitarlos

### Para Intermedios:
- Conecta con conceptos previos
- Incluye comparaciones y alternativas
- Muestra casos de uso prácticos
- Introduce buenas prácticas

### Para Avanzados:
- Profundiza en el "por qué" y "cómo"
- Incluye consideraciones de rendimiento
- Muestra patrones y arquitecturas
- Conecta con el ecosistema más amplio

### Para Expertos:
- Incluye detalles de implementación
- Muestra casos edge y soluciones
- Conecta con especificaciones ECMAScript
- Incluye análisis de rendimiento avanzado

---

## Ejemplo de Uso Completo

```
Genera un diagrama SVG educativo sobre "Promises y async/await" para nivel INTERMEDIO en JavaScript.

Requisitos específicos:
- Dimensiones: 1000x700px
- Incluir: sintaxis de promises, estados (pending/fulfilled/rejected), async/await, manejo de errores
- Mostrar: ejemplos de fetch API, comparación callback vs promise vs async/await
- Destacar: ventajas de async/await, manejo de errores con try/catch
- Conexiones: relacionar con callbacks y event loop básico

El diagrama debe ser autocontenido y permitir aprender el concepto sin recursos adicionales.
```