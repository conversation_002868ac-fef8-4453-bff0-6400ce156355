
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
        <marker id="arrowhead-normal" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
        </marker>
        <marker id="arrowhead-true" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
        </marker>
        <marker id="arrowhead-false" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#ea580c" />
        </marker>
        <marker id="arrowhead-process" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#2563eb" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Grafo de llamadas de funciones</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        <line x1="516.6666666666666" y1="250" x2="467.8511301977579" y2="367.8511301977579" 
                stroke="#2563eb" stroke-width="2" 
                marker-end="url(#arrowhead)"/><line x1="516.6666666666666" y1="250" x2="350" y2="416.66666666666663" 
                stroke="#2563eb" stroke-width="2" 
                marker-end="url(#arrowhead)"/><line x1="467.8511301977579" y1="367.8511301977579" x2="232.1488698022421" y2="367.8511301977579" 
                stroke="#2563eb" stroke-width="2" 
                marker-end="url(#arrowhead)"/><line x1="467.8511301977579" y1="367.8511301977579" x2="183.33333333333334" y2="250.00000000000003" 
                stroke="#2563eb" stroke-width="2" 
                marker-end="url(#arrowhead)"/><line x1="350" y1="416.66666666666663" x2="232.14886980224207" y2="132.1488698022421" 
                stroke="#2563eb" stroke-width="2" 
                marker-end="url(#arrowhead)"/><line x1="350" y1="416.66666666666663" x2="349.99999999999994" y2="83.33333333333334" 
                stroke="#2563eb" stroke-width="2" 
                marker-end="url(#arrowhead)"/><line x1="183.33333333333334" y1="250.00000000000003" x2="467.85113019775787" y2="132.14886980224207" 
                stroke="#2563eb" stroke-width="2" 
                marker-end="url(#arrowhead)"/>
        <g class="function-node">
            <rect x="456.66666666666663" y="220" width="120" height="60" rx="8"
                  fill="#10b981" fill-opacity="0.1" stroke="#10b981" stroke-width="2"/>
            <text x="516.6666666666666" y="255" text-anchor="middle" 
                  class="small-text" fill="#10b981">main()</text>
        </g>
        <g class="function-node">
            <rect x="407.8511301977579" y="337.8511301977579" width="120" height="60" rx="8"
                  fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="467.8511301977579" y="372.8511301977579" text-anchor="middle" 
                  class="small-text" fill="#2563eb">processData()</text>
        </g>
        <g class="function-node">
            <rect x="290" y="386.66666666666663" width="120" height="60" rx="8"
                  fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="350" y="421.66666666666663" text-anchor="middle" 
                  class="small-text" fill="#2563eb">validateInput()</text>
        </g>
        <g class="function-node">
            <rect x="172.1488698022421" y="337.8511301977579" width="120" height="60" rx="8"
                  fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="232.1488698022421" y="372.8511301977579" text-anchor="middle" 
                  class="small-text" fill="#2563eb">formatData()</text>
        </g>
        <g class="function-node">
            <rect x="123.33333333333334" y="220.00000000000003" width="120" height="60" rx="8"
                  fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="183.33333333333334" y="255.00000000000003" text-anchor="middle" 
                  class="small-text" fill="#2563eb">saveData()</text>
        </g>
        <g class="function-node">
            <rect x="172.14886980224207" y="102.1488698022421" width="120" height="60" rx="8"
                  fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="232.14886980224207" y="137.1488698022421" text-anchor="middle" 
                  class="small-text" fill="#2563eb">checkType()</text>
        </g>
        <g class="function-node">
            <rect x="289.99999999999994" y="53.33333333333334" width="120" height="60" rx="8"
                  fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="349.99999999999994" y="88.33333333333334" text-anchor="middle" 
                  class="small-text" fill="#2563eb">checkRange()</text>
        </g>
        <g class="function-node">
            <rect x="407.85113019775787" y="102.14886980224207" width="120" height="60" rx="8"
                  fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="467.85113019775787" y="137.14886980224207" text-anchor="middle" 
                  class="small-text" fill="#2563eb">logAction()</text>
        </g>
        <g class="code-group">
            <rect x="50" y="520" width="700" height="120" class="code-box" rx="4" />
            <text x="65" y="545" class="code">function main() { processData(); validateInput(); }</text>
<text x="65" y="561" class="code">function processData() { formatData(); saveData(); }</text>
<text x="65" y="577" class="code">function validateInput() { checkType(); checkRange(); }</text>
<text x="65" y="593" class="code">// ... más funciones</text>

        </g>
        <g class="box-group">
            <rect x="750" y="100" width="300" height="150" class="concept-box" rx="8" />
            <text x="765" y="125" class="subtitle">Grafo de Llamadas</text>
            <text x="765" y="150" class="text">Muestra las relaciones entre</text>
<text x="765" y="168" class="text">funciones. Útil para: análisis</text>
<text x="765" y="186" class="text">de dependencias, detección de</text>
<text x="765" y="204" class="text">funciones no utilizadas,</text>
<text x="765" y="222" class="text">optimización de código.</text>

        </g>
    </g>
</svg>