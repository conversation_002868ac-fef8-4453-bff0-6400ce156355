
<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
        <marker id="arrowhead-normal" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
        </marker>
        <marker id="arrowhead-true" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
        </marker>
        <marker id="arrowhead-false" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#ea580c" />
        </marker>
        <marker id="arrowhead-process" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#2563eb" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Línea de tiempo de ejecución</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
        <line x1="100" y1="200" x2="900" y2="200" stroke="#1e293b" stroke-width="2"/>
        <text x="500" y="190" text-anchor="middle" class="subtitle">Línea de Tiempo de Ejecución</text>
            <line x1="100" y1="195" x2="100" y2="205" stroke="#1e293b" stroke-width="1"/>
            <text x="100" y="220" text-anchor="middle" class="small-text">0ms</text>
            <line x1="233.33333333333331" y1="195" x2="233.33333333333331" y2="205" stroke="#1e293b" stroke-width="1"/>
            <text x="233.33333333333331" y="220" text-anchor="middle" class="small-text">5ms</text>
            <line x1="366.66666666666663" y1="195" x2="366.66666666666663" y2="205" stroke="#1e293b" stroke-width="1"/>
            <text x="366.66666666666663" y="220" text-anchor="middle" class="small-text">10ms</text>
            <line x1="500" y1="195" x2="500" y2="205" stroke="#1e293b" stroke-width="1"/>
            <text x="500" y="220" text-anchor="middle" class="small-text">15ms</text>
            <line x1="633.3333333333333" y1="195" x2="633.3333333333333" y2="205" stroke="#1e293b" stroke-width="1"/>
            <text x="633.3333333333333" y="220" text-anchor="middle" class="small-text">20ms</text>
            <line x1="766.6666666666667" y1="195" x2="766.6666666666667" y2="205" stroke="#1e293b" stroke-width="1"/>
            <text x="766.6666666666667" y="220" text-anchor="middle" class="small-text">25ms</text>
            <line x1="900" y1="195" x2="900" y2="205" stroke="#1e293b" stroke-width="1"/>
            <text x="900" y="220" text-anchor="middle" class="small-text">30ms</text>
            <rect x="100" y="160" width="26.666666666666668" height="20" fill="#2563eb"
                  fill-opacity="0.7" stroke="#2563eb" stroke-width="1" rx="2"/>
            <text x="113.33333333333333" y="175" text-anchor="middle" class="small-text"
                  fill="white">console.log("start")</text>
            <rect x="153.33333333333334" y="140" width="26.666666666666668" height="20" fill="#ea580c"
                  fill-opacity="0.7" stroke="#ea580c" stroke-width="1" rx="2"/>
            <text x="166.66666666666669" y="155" text-anchor="middle" class="small-text"
                  fill="white">setTimeout(cb, 10)</text>
            <rect x="206.66666666666669" y="160" width="26.666666666666668" height="20" fill="#2563eb"
                  fill-opacity="0.7" stroke="#2563eb" stroke-width="1" rx="2"/>
            <text x="220.00000000000003" y="175" text-anchor="middle" class="small-text"
                  fill="white">Promise.resolve()</text>
            <rect x="260" y="120" width="53.333333333333336" height="20" fill="undefined"
                  fill-opacity="0.7" stroke="undefined" stroke-width="1" rx="2"/>
            <text x="286.6666666666667" y="135" text-anchor="middle" class="small-text"
                  fill="white">promise.then()</text>
            <rect x="366.66666666666663" y="160" width="26.666666666666668" height="20" fill="#2563eb"
                  fill-opacity="0.7" stroke="#2563eb" stroke-width="1" rx="2"/>
            <text x="379.99999999999994" y="175" text-anchor="middle" class="small-text"
                  fill="white">console.log("end")</text>
            <rect x="420" y="180" width="53.333333333333336" height="20" fill="#16a34a"
                  fill-opacity="0.7" stroke="#16a34a" stroke-width="1" rx="2"/>
            <text x="446.6666666666667" y="195" text-anchor="middle" class="small-text"
                  fill="white">setTimeout callback</text>
            <rect x="100" y="350" width="20" height="15" fill="#2563eb" rx="2"/>
            <text x="130" y="362" class="small-text">Synchronous</text>
            <rect x="250" y="350" width="20" height="15" fill="#ea580c" rx="2"/>
            <text x="280" y="362" class="small-text">Async API Call</text>
            <rect x="400" y="350" width="20" height="15" fill="undefined" rx="2"/>
            <text x="430" y="362" class="small-text">Microtask</text>
            <rect x="550" y="350" width="20" height="15" fill="#16a34a" rx="2"/>
            <text x="580" y="362" class="small-text">Callback</text>
    </g>
</svg>