{"version": 3, "file": "Browser.js", "sourceRoot": "", "sources": ["../../../../../src/bidi/core/Browser.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKH,kEAA0D;AAC1D,4DAA0E;AAC1E,4DAAwE;AAGxE,yCAA6C;AAE7C,qDAA6C;AAY7C;;GAEG;IACU,OAAO;sBAAS,8BAAY;;;;;;;;;;iBAA5B,OAAQ,SAAQ,WAgB3B;;;YAgHA,wKAAA,OAAO,6DAIN;YAMD,kKAAM,KAAK,6DAMV;YAMD,mMAAM,gBAAgB,6DAcrB;YAMD,gMAAM,eAAe,6DAIpB;YAMD,4MAAM,mBAAmB,6DAIxB;YAMD,sMAAM,iBAAiB,6DAkBtB;YAMD,mMAAM,gBAAgB,6DAOrB;YAMD,yMAAM,kBAAkB,6DAEvB;;;QApND,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAgB;YAChC,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,OAAO,CAAC,WAAW,EAAE,CAAC;YAC5B,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,OAAO,IAvBI,mDAAO,EAuBR,KAAK,EAAC;QAChB,OAAO,CAAqB;QACnB,YAAY,GAAG,IAAI,+BAAe,EAAE,CAAC;QACrC,aAAa,GAAG,IAAI,GAAG,EAAuB,CAAC;QAC/C,OAAO,CAAU;QACjB,cAAc,GAAG,IAAI,GAAG,EAA6B,CAAC;QAE/D,YAAoB,OAAgB;YAClC,KAAK,EAAE,CAAC;YAER,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACzB,CAAC;QAED,KAAK,CAAC,WAAW;YACf,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAC1C,IAAI,8BAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAC/B,CAAC;YACF,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAC,MAAM,EAAC,EAAE,EAAE;gBACxC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC;YAEH,cAAc,CAAC,EAAE,CAAC,qBAAqB,EAAE,IAAI,CAAC,EAAE;gBAC9C,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;oBAClC,OAAO;gBACT,CAAC;gBACD,IAAI,CAAC,cAAc,CAAC,GAAG,CACrB,IAAI,CAAC,KAAK,EACV,4BAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CACtD,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC/B,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACrC,CAAC;QAED,KAAK,CAAC,iBAAiB;YACrB,MAAM,EACJ,MAAM,EAAE,EAAC,YAAY,EAAC,GACvB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC;YAE3D,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE,CAAC;gBACnC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;QAED,KAAK,CAAC,qBAAqB;YACzB,0EAA0E;YAC1E,sBAAsB;YACtB,MAAM,UAAU,GAAG,IAAI,GAAG,EAAU,CAAC;YACrC,IAAI,QAAqC,CAAC;YAE1C,CAAC;;;oBACC,MAAM,cAAc,kCAAG,IAAI,8BAAY,CAAC,IAAI,CAAC,OAAO,CAAC,QAAA,CAAC;oBACtD,cAAc,CAAC,EAAE,CAAC,gCAAgC,EAAE,IAAI,CAAC,EAAE;wBACzD,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC/B,CAAC,CAAC,CAAC;oBACH,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC;oBACxE,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;;;;;;;;;aAC5B;YAED,uDAAuD;YACvD,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;gBAC5B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;oBAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gCAAgC,EAAE,IAAI,CAAC,CAAC;gBAC5D,CAAC;gBACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;QACH,CAAC;QAED,kBAAkB,CAAC,EAAU;YAC3B,MAAM,WAAW,GAAG,4BAAW,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACjD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;YAEpD,MAAM,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAC9C,IAAI,8BAAY,CAAC,WAAW,CAAC,CAC9B,CAAC;YACF,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACrC,kBAAkB,CAAC,kBAAkB,EAAE,CAAC;gBAExC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;YAEH,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,IAAI,MAAM;YACR,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;QACD,IAAI,kBAAkB;YACpB,mEAAmE;YACnE,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,4BAAW,CAAC,OAAO,CAAE,CAAC;QACtD,CAAC;QACD,IAAI,YAAY;YACd,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC;QACpC,CAAC;QACD,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,YAAY,CAAC;QAC3B,CAAC;QACD,IAAI,YAAY;YACd,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QACrC,CAAC;QAGD,OAAO,CAAC,MAAe,EAAE,MAAM,GAAG,KAAK;YACrC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,6BAAa,CAAC,EAAE,CAAC;QACxB,CAAC;QAMD,KAAK,CAAC,KAAK;YACT,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;YAC/C,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,OAAO,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;QAMD,KAAK,CAAC,gBAAgB,CACpB,mBAA2B,EAC3B,UAAmC,EAAE;YAErC,MAAM,EACJ,MAAM,EAAE,EAAC,MAAM,EAAC,GACjB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBACrD,mBAAmB;gBACnB,GAAG,OAAO;gBACV,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE;oBACxC,OAAO,OAAO,CAAC,EAAE,CAAC;gBACpB,CAAC,CAA0B;aAC5B,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC;QAMD,KAAK,CAAC,eAAe,CAAC,SAAiC;YACrD,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBACjD,SAAS;aACV,CAAC,CAAC;QACL,CAAC;QAMD,KAAK,CAAC,mBAAmB,CAAC,MAAc;YACtC,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACpD,MAAM;aACP,CAAC,CAAC;QACL,CAAC;QAMD,KAAK,CAAC,iBAAiB,CACrB,OAA8B;YAE9B,MAAM,WAAW,GACf,OAAO,CAAC,WAAW,KAAK,SAAS;gBAC/B,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC;oBACE,SAAS,EAAE,QAAQ;oBACnB,SAAS,EAAE,OAAO,CAAC,WAAW;oBAC9B,QAAQ,EAAE,OAAO,CAAC,WAAW;oBAC7B,OAAO,EAAE,OAAO,CAAC,eAAe;iBACjC,CAAC;YACR,MAAM,EACJ,MAAM,EAAE,EAAC,WAAW,EAAE,OAAO,EAAC,GAC/B,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACvD,KAAK,EAAE,WAAW;aACnB,CAAC,CAAC;YACH,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAC1C,CAAC;QAMD,KAAK,CAAC,gBAAgB,CAAC,IAAY;YACjC,MAAM,EACJ,MAAM,EAAE,EAAC,SAAS,EAAC,GACpB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAClD,aAAa,EAAE,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAC;aACpC,CAAC,CAAC;YACH,OAAO,SAAS,CAAC;QACnB,CAAC;QAMD,KAAK,CAAC,kBAAkB,CAAC,EAAU;YACjC,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAC,SAAS,EAAE,EAAE,EAAC,CAAC,CAAC;QACrE,CAAC;QAEQ,yBAxGR,+BAAe,wBAOf,IAAA,+BAAe,EAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,mCASD,IAAA,+BAAe,EAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,kCAiBD,IAAA,+BAAe,EAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,sCAOD,IAAA,+BAAe,EAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,oCAOD,IAAA,+BAAe,EAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,mCAqBD,IAAA,+BAAe,EAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,qCAUD,IAAA,+BAAe,EAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,GAKQ,6BAAa,EAAC;YACtB,IAAI,CAAC,OAAO;gBACV,+DAA+D,CAAC;YAClE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC;YAC9C,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC;YAElD,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC5B,KAAK,CAAC,6BAAa,CAAC,EAAE,CAAC;QACzB,CAAC;;;AAjPU,0BAAO"}