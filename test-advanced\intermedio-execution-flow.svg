
<svg width="1000" height="700" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
        <marker id="arrowhead-normal" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
        </marker>
        <marker id="arrowhead-true" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
        </marker>
        <marker id="arrowhead-false" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#ea580c" />
        </marker>
        <marker id="arrowhead-process" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#2563eb" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Flujo de ejecución de funciones</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
        <g class="connection">
            <path d="M 410 110 L 410 150" stroke="#6b7280" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-normal)"/>
            
        </g>
        <g class="connection">
            <path d="M 410 210 L 210 250" stroke="#6b7280" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-normal)"/>
            
        </g>
        <g class="connection">
            <path d="M 210 310 L 210 350" stroke="#6b7280" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-normal)"/>
            
        </g>
        <g class="connection">
            <path d="M 210 410 L 410 450" stroke="#6b7280" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-normal)"/>
            
        </g>
        <g class="connection">
            <path d="M 410 510 L 210 450" stroke="#6b7280" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-normal)"/>
            
        </g>
        <g class="connection">
            <path d="M 210 510 Q 210 430 410 350" stroke="#6b7280" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-normal)"/>
            
        </g>
        <g class="connection">
            <path d="M 410 410 Q 410 480 410 550" stroke="#6b7280" stroke-width="2" 
                  fill="none" marker-end="url(#arrowhead-normal)"/>
            
        </g>
        <g class="flow-node" data-id="main">
            <ellipse cx="410" cy="80" rx="60" ry="30" 
                         fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="410" y="80" 
                            text-anchor="middle" class="small-text" fill="#10b981">main()</text>

        </g>
        <g class="flow-node" data-id="call1">
            <rect x="350" y="150" width="120" height="60" rx="8" 
                         fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="410" y="174" 
                            text-anchor="middle" class="small-text" fill="#2563eb">llamar</text>
<text x="410" y="186" 
                            text-anchor="middle" class="small-text" fill="#2563eb">función A</text>

        </g>
        <g class="flow-node" data-id="funcA">
            <rect x="150" y="250" width="120" height="60" rx="8" 
                         fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="210" y="280" 
                            text-anchor="middle" class="small-text" fill="#2563eb">función A()</text>

        </g>
        <g class="flow-node" data-id="call2">
            <rect x="150" y="350" width="120" height="60" rx="8" 
                         fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="210" y="374" 
                            text-anchor="middle" class="small-text" fill="#2563eb">llamar</text>
<text x="210" y="386" 
                            text-anchor="middle" class="small-text" fill="#2563eb">función B</text>

        </g>
        <g class="flow-node" data-id="funcB">
            <rect x="350" y="450" width="120" height="60" rx="8" 
                         fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="410" y="480" 
                            text-anchor="middle" class="small-text" fill="#2563eb">función B()</text>

        </g>
        <g class="flow-node" data-id="return2">
            <rect x="150" y="450" width="120" height="60" rx="8" 
                         fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="210" y="480" 
                            text-anchor="middle" class="small-text" fill="#2563eb">retornar a A</text>

        </g>
        <g class="flow-node" data-id="return1">
            <rect x="350" y="350" width="120" height="60" rx="8" 
                         fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="410" y="374" 
                            text-anchor="middle" class="small-text" fill="#2563eb">retornar a</text>
<text x="410" y="386" 
                            text-anchor="middle" class="small-text" fill="#2563eb">main</text>

        </g>
        <g class="flow-node" data-id="end">
            <ellipse cx="410" cy="580" rx="60" ry="30" 
                         fill="#2563eb" fill-opacity="0.1" stroke="#2563eb" stroke-width="2"/>
            <text x="410" y="580" 
                            text-anchor="middle" class="small-text" fill="#ea580c">Fin</text>

        </g>
        <g class="code-group">
            <rect x="500" y="150" width="280" height="200" class="code-box" rx="4" />
            <text x="515" y="175" class="code">function main() {</text>
<text x="515" y="191" class="code">  console.log(&quot;Inicio&quot;);</text>
<text x="515" y="207" class="code">  funcionA();</text>
<text x="515" y="223" class="code">  console.log(&quot;Fin&quot;);</text>
<text x="515" y="239" class="code">}</text>
<text x="515" y="255" class="code"></text>
<text x="515" y="271" class="code">function funcionA() {</text>
<text x="515" y="287" class="code">  console.log(&quot;En A&quot;);</text>
<text x="515" y="303" class="code">  funcionB();</text>
<text x="515" y="319" class="code">  console.log(&quot;Saliendo de A&quot;);</text>
<text x="515" y="335" class="code">}</text>
<text x="515" y="351" class="code"></text>
<text x="515" y="367" class="code">function funcionB() {</text>
<text x="515" y="383" class="code">  console.log(&quot;En B&quot;);</text>
<text x="515" y="399" class="code">}</text>

        </g>
    </g>
</svg>