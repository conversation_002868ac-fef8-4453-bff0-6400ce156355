
<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Event loop y call stack</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
        <!-- Fundamentos teóricos -->
        
        <g class="box-group">
            <rect x="50" y="80" width="1100" height="80" class="concept-box" rx="8" />
            <text x="65" y="105" class="subtitle">Fundamentos teóricos</text>
            <text x="65" y="130" class="text">JavaScript es single-threaded pero puede manejar operaciones asíncronas gracias al Event Loop. El Event Loop coordina la ejecución entre el Call Stack, Callback Queue, y Microtask Queue.</text>
        </g>
        
        <!-- Componentes del motor JS -->
        
        <g class="box-group">
            <rect x="50" y="180" width="350" height="180" class="concept-box" rx="8" />
            <text x="65" y="205" class="subtitle">Componentes del motor JS</text>
            <text x="65" y="230" class="text">Call Stack: Pila de ejecución
Heap: Memoria para objetos
Callback Queue: Cola de callbacks
Microtask Queue: Cola de microtareas
Event Loop: Coordinador
Web APIs: setTimeout, fetch, etc.</text>
        </g>
        
        <!-- Diagrama de flujo -->
        <g>
            <!-- Call Stack -->
            <rect x="450" y="180" width="200" height="180" fill="#e2e8f0" stroke="#2563eb" stroke-width="2" />
            <text x="550" y="200" text-anchor="middle" class="subtitle">Call Stack</text>
            <rect x="470" y="220" width="160" height="30" fill="#bfdbfe" stroke="#2563eb" />
            <text x="550" y="240" text-anchor="middle" class="small-text">main()</text>
            <rect x="470" y="260" width="160" height="30" fill="#bfdbfe" stroke="#2563eb" />
            <text x="550" y="280" text-anchor="middle" class="small-text">function1()</text>
            <rect x="470" y="300" width="160" height="30" fill="#bfdbfe" stroke="#2563eb" />
            <text x="550" y="320" text-anchor="middle" class="small-text">function2()</text>
            
            <!-- Web APIs -->
            <rect x="700" y="180" width="200" height="180" fill="#e2e8f0" stroke="#16a34a" stroke-width="2" />
            <text x="800" y="200" text-anchor="middle" class="subtitle">Web APIs</text>
            <rect x="720" y="220" width="160" height="30" fill="#dcfce7" stroke="#16a34a" />
            <text x="800" y="240" text-anchor="middle" class="small-text">setTimeout()</text>
            <rect x="720" y="260" width="160" height="30" fill="#dcfce7" stroke="#16a34a" />
            <text x="800" y="280" text-anchor="middle" class="small-text">fetch()</text>
            <rect x="720" y="300" width="160" height="30" fill="#dcfce7" stroke="#16a34a" />
            <text x="800" y="320" text-anchor="middle" class="small-text">addEventListener()</text>
            
            <!-- Callback Queue -->
            <rect x="450" y="400" width="200" height="100" fill="#e2e8f0" stroke="#ea580c" stroke-width="2" />
            <text x="550" y="420" text-anchor="middle" class="subtitle">Callback Queue</text>
            <rect x="470" y="440" width="160" height="30" fill="#fed7aa" stroke="#ea580c" />
            <text x="550" y="460" text-anchor="middle" class="small-text">setTimeout callback</text>
            
            <!-- Microtask Queue -->
            <rect x="700" y="400" width="200" height="100" fill="#e2e8f0" stroke="#8b5cf6" stroke-width="2" />
            <text x="800" y="420" text-anchor="middle" class="subtitle">Microtask Queue</text>
            <rect x="720" y="440" width="160" height="30" fill="#ddd6fe" stroke="#8b5cf6" />
            <text x="800" y="460" text-anchor="middle" class="small-text">Promise callback</text>
            
            <!-- Event Loop -->
            <ellipse cx="600" cy="550" rx="80" ry="40" fill="#f8fafc" stroke="#1e293b" stroke-width="2" />
            <text x="600" y="555" text-anchor="middle" class="subtitle">Event Loop</text>
            
            <!-- Flechas -->
            <line x1="550" y1="360" x2="550" y2="400" class="arrow" />
            <line x1="800" y1="360" x2="800" y2="400" class="arrow" />
            <line x1="650" y1="180" x2="700" y2="180" class="arrow" />
            <line x1="700" y1="360" x2="650" y2="360" class="arrow" />
            <line x1="550" y1="500" x2="550" y2="550" class="arrow" />
            <line x1="800" y1="500" x2="650" y2="550" class="arrow" />
            <line x1="550" y1="550" x2="550" y2="500" class="arrow" />
        </g>
        
        <!-- Ejemplo de código -->
        
        <g class="code-group">
            <rect x="50" y="520" width="350" height="180" class="code-box" rx="4" />
            <text x="65" y="545" class="code">console.log(&quot;Start&quot;);</text>
<text x="65" y="563" class="code"></text>
<text x="65" y="581" class="code">setTimeout(() =&gt; {</text>
<text x="65" y="599" class="code">  console.log(&quot;Timeout&quot;);</text>
<text x="65" y="617" class="code">}, 0);</text>
<text x="65" y="635" class="code"></text>
<text x="65" y="653" class="code">Promise.resolve().then(() =&gt; {</text>
<text x="65" y="671" class="code">  console.log(&quot;Promise&quot;);</text>
<text x="65" y="689" class="code">});</text>
<text x="65" y="707" class="code"></text>
<text x="65" y="725" class="code">console.log(&quot;End&quot;);</text>
<text x="65" y="743" class="code"></text>
<text x="65" y="761" class="code">// Output: Start, End, Promise, Timeout</text>

        </g>
        
        <!-- Análisis de rendimiento -->
        
        <g class="box-group">
            <rect x="50" y="720" width="1100" height="80" class="warning-box" rx="8" />
            <text x="65" y="745" class="subtitle">Análisis de rendimiento</text>
            <text x="65" y="770" class="text">Bloquear el Event Loop causa jank (UI no responde). Las tareas largas deben dividirse. Las microtareas tienen prioridad sobre macrotareas. Usar requestAnimationFrame para animaciones eficientes.</text>
        </g>
        
        <!-- Casos edge -->
        
        <g class="box-group">
            <rect x="50" y="820" width="530" height="60" class="warning-box" rx="8" />
            <text x="65" y="845" class="subtitle">Casos edge</text>
            <text x="65" y="870" class="text">Anidación de promesas. Recursión en microtareas. Interacción con requestAnimationFrame. Prioridad entre diferentes tipos de eventos.</text>
        </g>
        
        <!-- Tendencias futuras -->
        
        <g class="box-group">
            <rect x="620" y="820" width="530" height="60" class="example-box" rx="8" />
            <text x="635" y="845" class="subtitle">Tendencias futuras</text>
            <text x="635" y="870" class="text">Web Workers. SharedArrayBuffer. Atomics API. Top-level await. Scheduler API.</text>
        </g>
        
    </g>
</svg>