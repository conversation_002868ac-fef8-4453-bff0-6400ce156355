const BaseGenerator = require('./BaseGenerator');

class ExpertGenerator extends BaseGenerator {
    constructor() {
        super();
        
        // Definir temas disponibles para nivel experto
        this.topics = {
            'event-loop': 'Event loop y call stack',
            'memory-management': 'Memory management',
            'microtasks': 'Micro/macro tasks',
            'advanced-patterns': 'Advanced patterns (Observer, Factory, etc.)',
            'metaprogramming': 'Metaprogramming',
            'typescript': 'TypeScript integration',
            'performance-profiling': 'Performance profiling',
            'v8-internals': 'V8 Engine internals',
            'compilation': 'JIT compilation',
            'garbage-collection': 'Garbage collection avanzado',
            'web-workers': 'Web Workers y threading',
            'shared-array-buffer': 'SharedArrayBuffer y Atomics',
            'wasm-integration': 'WebAssembly integration',
            'node-internals': 'Node.js internals',
            'security': 'Seguridad en JavaScript',
            'frameworks-internals': 'Internals de frameworks',
            'build-tools': 'Build tools avanzados',
            'micro-frontends': 'Micro frontends',
            'pwa-advanced': 'PWA avanzado'
        };
        
        // Ejemplos predefinidos
        this.examples = {
            'event-loop': this.generateEventLoopExample(),
            'memory-management': this.generateMemoryManagementExample(),
            'microtasks': this.generateMicrotasksExample(),
            'advanced-patterns': this.generateAdvancedPatternsExample(),
            'metaprogramming': this.generateMetaprogrammingExample(),
            'typescript': this.generateTypeScriptExample(),
            'performance-profiling': this.generatePerformanceProfilingExample()
        };
    }

    /**
     * Genera un diagrama SVG para un tema de nivel experto
     * @param {string} topic - Tema a generar
     * @param {object} options - Opciones adicionales
     * @returns {string} - SVG generado
     */
    generate(topic, options = {}) {
        const normalizedTopic = topic.toLowerCase();
        
        if (!this.hasTopicSupport(normalizedTopic)) {
            throw new Error(`El tema "${topic}" no está soportado para nivel experto`);
        }
        
        // Obtener el título completo del tema
        const title = this.topics[normalizedTopic];
        
        // Generar el SVG base con dimensiones más grandes para contenido complejo
        const svgOptions = { width: 1200, height: 900, ...options };
        let svg = this.generateBaseSVG(title, svgOptions);
        
        // Generar el contenido específico según el tema
        let content = '';
        switch (normalizedTopic) {
            case 'event-loop':
                content = this.generateEventLoopContent();
                break;
            case 'memory-management':
                content = this.generateMemoryManagementContent();
                break;
            case 'microtasks':
                content = this.generateMicrotasksContent();
                break;
            case 'advanced-patterns':
                content = this.generateAdvancedPatternsContent();
                break;
            case 'metaprogramming':
                content = this.generateMetaprogrammingContent();
                break;
            case 'typescript':
                content = this.generateTypeScriptContent();
                break;
            case 'performance-profiling':
                content = this.generatePerformanceProfilingContent();
                break;
            default:
                content = '<text x="600" y="450" text-anchor="middle" class="text">Contenido no disponible</text>';
        }
        
        // Insertar el contenido en el SVG
        svg = svg.replace('<!-- El contenido específico se añadirá aquí -->', content);
        
        return svg;
    }

    /**
     * Genera el contenido para el tema de event loop
     * @returns {string} - SVG del contenido
     */
    generateEventLoopContent() {
        return `
        <!-- Fundamentos teóricos -->
        ${this.createBox(50, 80, 1100, 80, 'Fundamentos teóricos', 'JavaScript es single-threaded pero puede manejar operaciones asíncronas gracias al Event Loop. El Event Loop coordina la ejecución entre el Call Stack, Callback Queue, y Microtask Queue.', 'concept')}
        
        <!-- Componentes del motor JS -->
        ${this.createBox(50, 180, 350, 180, 'Componentes del motor JS', 'Call Stack: Pila de ejecución\nHeap: Memoria para objetos\nCallback Queue: Cola de callbacks\nMicrotask Queue: Cola de microtareas\nEvent Loop: Coordinador\nWeb APIs: setTimeout, fetch, etc.', 'concept')}
        
        <!-- Diagrama de flujo -->
        <g>
            <!-- Call Stack -->
            <rect x="450" y="180" width="200" height="180" fill="#e2e8f0" stroke="#2563eb" stroke-width="2" />
            <text x="550" y="200" text-anchor="middle" class="subtitle">Call Stack</text>
            <rect x="470" y="220" width="160" height="30" fill="#bfdbfe" stroke="#2563eb" />
            <text x="550" y="240" text-anchor="middle" class="small-text">main()</text>
            <rect x="470" y="260" width="160" height="30" fill="#bfdbfe" stroke="#2563eb" />
            <text x="550" y="280" text-anchor="middle" class="small-text">function1()</text>
            <rect x="470" y="300" width="160" height="30" fill="#bfdbfe" stroke="#2563eb" />
            <text x="550" y="320" text-anchor="middle" class="small-text">function2()</text>
            
            <!-- Web APIs -->
            <rect x="700" y="180" width="200" height="180" fill="#e2e8f0" stroke="#16a34a" stroke-width="2" />
            <text x="800" y="200" text-anchor="middle" class="subtitle">Web APIs</text>
            <rect x="720" y="220" width="160" height="30" fill="#dcfce7" stroke="#16a34a" />
            <text x="800" y="240" text-anchor="middle" class="small-text">setTimeout()</text>
            <rect x="720" y="260" width="160" height="30" fill="#dcfce7" stroke="#16a34a" />
            <text x="800" y="280" text-anchor="middle" class="small-text">fetch()</text>
            <rect x="720" y="300" width="160" height="30" fill="#dcfce7" stroke="#16a34a" />
            <text x="800" y="320" text-anchor="middle" class="small-text">addEventListener()</text>
            
            <!-- Callback Queue -->
            <rect x="450" y="400" width="200" height="100" fill="#e2e8f0" stroke="#ea580c" stroke-width="2" />
            <text x="550" y="420" text-anchor="middle" class="subtitle">Callback Queue</text>
            <rect x="470" y="440" width="160" height="30" fill="#fed7aa" stroke="#ea580c" />
            <text x="550" y="460" text-anchor="middle" class="small-text">setTimeout callback</text>
            
            <!-- Microtask Queue -->
            <rect x="700" y="400" width="200" height="100" fill="#e2e8f0" stroke="#8b5cf6" stroke-width="2" />
            <text x="800" y="420" text-anchor="middle" class="subtitle">Microtask Queue</text>
            <rect x="720" y="440" width="160" height="30" fill="#ddd6fe" stroke="#8b5cf6" />
            <text x="800" y="460" text-anchor="middle" class="small-text">Promise callback</text>
            
            <!-- Event Loop -->
            <ellipse cx="600" cy="550" rx="80" ry="40" fill="#f8fafc" stroke="#1e293b" stroke-width="2" />
            <text x="600" y="555" text-anchor="middle" class="subtitle">Event Loop</text>
            
            <!-- Flechas -->
            ${this.createArrow(550, 360, 550, 400, '')}
            ${this.createArrow(800, 360, 800, 400, '')}
            ${this.createArrow(650, 180, 700, 180, '')}
            ${this.createArrow(700, 360, 650, 360, '')}
            ${this.createArrow(550, 500, 550, 550, '')}
            ${this.createArrow(800, 500, 650, 550, '')}
            ${this.createArrow(550, 550, 550, 500, '')}
        </g>
        
        <!-- Ejemplo de código -->
        ${this.createCodeBox(50, 520, 350, 180, 'console.log("Start");\n\nsetTimeout(() => {\n  console.log("Timeout");\n}, 0);\n\nPromise.resolve().then(() => {\n  console.log("Promise");\n});\n\nconsole.log("End");\n\n// Output: Start, End, Promise, Timeout')}
        
        <!-- Análisis de rendimiento -->
        ${this.createBox(50, 720, 1100, 80, 'Análisis de rendimiento', 'Bloquear el Event Loop causa jank (UI no responde). Las tareas largas deben dividirse. Las microtareas tienen prioridad sobre macrotareas. Usar requestAnimationFrame para animaciones eficientes.', 'warning')}
        
        <!-- Casos edge -->
        ${this.createBox(50, 820, 530, 60, 'Casos edge', 'Anidación de promesas. Recursión en microtareas. Interacción con requestAnimationFrame. Prioridad entre diferentes tipos de eventos.', 'warning')}
        
        <!-- Tendencias futuras -->
        ${this.createBox(620, 820, 530, 60, 'Tendencias futuras', 'Web Workers. SharedArrayBuffer. Atomics API. Top-level await. Scheduler API.', 'example')}
        `;
    }

    // Métodos para generar contenido de otros temas (implementación básica)
    generateMemoryManagementContent() {
        return `<!-- Contenido para memory management -->`;
    }

    generateMicrotasksContent() {
        return `<!-- Contenido para microtasks -->`;
    }

    generateAdvancedPatternsContent() {
        return `<!-- Contenido para advanced patterns -->`;
    }

    generateMetaprogrammingContent() {
        return `<!-- Contenido para metaprogramming -->`;
    }

    generateTypeScriptContent() {
        return `<!-- Contenido para TypeScript -->`;
    }

    generatePerformanceProfilingContent() {
        return `<!-- Contenido para performance profiling -->`;
    }

    // Métodos para generar ejemplos
    generateEventLoopExample() {
        return {
            svg: this.generate('event-loop'),
            description: 'Diagrama experto que explica el Event Loop en JavaScript, incluyendo call stack, heap, callback queue, microtask queue y el flujo completo de ejecución.'
        };
    }

    generateMemoryManagementExample() {
        return {
            svg: this.generate('memory-management'),
            description: 'Diagrama que explica cómo JavaScript gestiona la memoria, incluyendo garbage collection y cómo evitar memory leaks.'
        };
    }

    generateMicrotasksExample() {
        return {
            svg: this.generate('microtasks'),
            description: 'Diagrama que explica la diferencia entre microtasks y macrotasks, y cómo afectan al orden de ejecución.'
        };
    }

    generateAdvancedPatternsExample() {
        return {
            svg: this.generate('advanced-patterns'),
            description: 'Diagrama que muestra patrones de diseño avanzados en JavaScript y sus implementaciones.'
        };
    }

    generateMetaprogrammingExample() {
        return {
            svg: this.generate('metaprogramming'),
            description: 'Diagrama que explica técnicas de metaprogramación en JavaScript como Proxies, Reflect API y Symbol.'
        };
    }

    generateTypeScriptExample() {
        return {
            svg: this.generate('typescript'),
            description: 'Diagrama que muestra cómo integrar TypeScript con JavaScript y sus beneficios.'
        };
    }

    generatePerformanceProfilingExample() {
        return {
            svg: this.generate('performance-profiling'),
            description: 'Diagrama que explica cómo realizar profiling de rendimiento en aplicaciones JavaScript.'
        };
    }
}

module.exports = ExpertGenerator;
