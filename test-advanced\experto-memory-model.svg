
<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title { font-family: 'Segoe UI', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1e293b; }
            .subtitle { font-family: 'Segoe UI', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; }
            .text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 14px; fill: #1e293b; }
            .code { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #374151; }
            .small-text { font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; fill: #1e293b; }
            .concept-box { fill: #2563eb; fill-opacity: 0.1; stroke: #2563eb; stroke-width: 2; }
            .example-box { fill: #16a34a; fill-opacity: 0.1; stroke: #16a34a; stroke-width: 2; }
            .warning-box { fill: #ea580c; fill-opacity: 0.1; stroke: #ea580c; stroke-width: 2; }
            .code-box { fill: #f1f5f9; stroke: #e2e8f0; stroke-width: 1; }
            .arrow { stroke: #1e293b; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#1e293b" />
        </marker>
        <marker id="arrowhead-normal" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
        </marker>
        <marker id="arrowhead-true" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
        </marker>
        <marker id="arrowhead-false" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#ea580c" />
        </marker>
        <marker id="arrowhead-process" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#2563eb" />
        </marker>
    </defs>
    
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#f8fafc" />
    
    <!-- Título principal -->
    <text x="400" y="40" text-anchor="middle" class="title">Modelo de memoria visual</text>
    
    <!-- Contenido del diagrama -->
    <g id="content">
        
        <rect x="100" y="100" width="200" height="300" fill="#2563eb"
              fill-opacity="0.1" stroke="#2563eb" stroke-width="2" rx="8"/>
        <text x="200" y="125" text-anchor="middle" class="subtitle" fill="#2563eb">
            Stack Memory
        </text>
            <rect x="110" y="350" width="180" height="60" fill="#2563eb"
                  fill-opacity="0.2" stroke="#2563eb" stroke-width="1" rx="4"/>
            <text x="120" y="370" class="small-text" fill="#2563eb">
                main()
            </text>
                <text x="120" y="385" class="small-text" fill="#1e293b">
                    x: 5
                </text>
                <text x="120" y="397" class="small-text" fill="#1e293b">
                    y: 10
                </text>
            <rect x="110" y="280" width="180" height="60" fill="#2563eb"
                  fill-opacity="0.2" stroke="#2563eb" stroke-width="1" rx="4"/>
            <text x="120" y="300" class="small-text" fill="#2563eb">
                calculate()
            </text>
                <text x="120" y="315" class="small-text" fill="#1e293b">
                    a: 5
                </text>
                <text x="120" y="327" class="small-text" fill="#1e293b">
                    b: 10
                </text>
                <text x="120" y="339" class="small-text" fill="#1e293b">
                    result: ref→
                </text>
            <rect x="110" y="210" width="180" height="60" fill="#2563eb"
                  fill-opacity="0.2" stroke="#2563eb" stroke-width="1" rx="4"/>
            <text x="120" y="230" class="small-text" fill="#2563eb">
                helper()
            </text>
                <text x="120" y="245" class="small-text" fill="#1e293b">
                    temp: 15
                </text>
        <rect x="400" y="100" width="300" height="300" fill="#16a34a"
              fill-opacity="0.1" stroke="#16a34a" stroke-width="2" rx="8"/>
        <text x="550" y="125" text-anchor="middle" class="subtitle" fill="#16a34a">
            Heap Memory
        </text>
            <rect x="420" y="150" width="120" height="80" fill="#16a34a"
                  fill-opacity="0.2" stroke="#16a34a" stroke-width="1" rx="4"/>
            <text x="425" y="165" class="small-text" fill="#16a34a">
                Object @0x1234
            </text>
                <text x="425" y="180" class="small-text"
                      fill="#1e293b">name: "John"</text>
                <text x="425" y="192" class="small-text"
                      fill="#1e293b">age: 30</text>
            <rect x="420" y="250" width="120" height="80" fill="#16a34a"
                  fill-opacity="0.2" stroke="#16a34a" stroke-width="1" rx="4"/>
            <text x="425" y="265" class="small-text" fill="#16a34a">
                Array @0x5678
            </text>
                <text x="425" y="280" class="small-text"
                      fill="#1e293b">[0]: 1</text>
                <text x="425" y="292" class="small-text"
                      fill="#1e293b">[1]: 2</text>
                <text x="425" y="304" class="small-text"
                      fill="#1e293b">[2]: 3</text>
            <rect x="550" y="200" width="120" height="80" fill="#16a34a"
                  fill-opacity="0.2" stroke="#16a34a" stroke-width="1" rx="4"/>
            <text x="555" y="215" class="small-text" fill="#16a34a">
                Function @0x9ABC
            </text>
                <text x="555" y="230" class="small-text"
                      fill="#1e293b">code: {...}</text>
                <text x="555" y="242" class="small-text"
                      fill="#1e293b">scope: ref→</text>
        <line x1="290" y1="320" x2="420" y2="190" stroke="#ea580c"
              stroke-width="2" marker-end="url(#arrowhead)" stroke-dasharray="5,5"/>
        <text x="350" y="250" class="small-text" fill="#ea580c">reference</text>
        <rect x="750" y="100" width="150" height="150" fill="undefined"
              fill-opacity="0.1" stroke="undefined" stroke-width="2" rx="8"/>
        <text x="825" y="125" text-anchor="middle" class="subtitle" fill="undefined">
            GC Queue
        </text>
        <text x="760" y="150" class="small-text" fill="undefined">Unreachable:</text>
        <text x="760" y="170" class="small-text" fill="undefined">• Object @0xDEAD</text>
        <text x="760" y="190" class="small-text" fill="undefined">• Array @0xBEEF</text>
    </g>
</svg>